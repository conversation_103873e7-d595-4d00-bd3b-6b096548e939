"""
Pygame-based GUI for eye tracking calibration procedure.
Replaces OpenCV-based GUI with pygame for better cross-platform compatibility.
"""

import os
os.environ['PYGAME_HIDE_SUPPORT_PROMPT'] = '1'
import pygame
import numpy as np
import time
from typing import Optional, Tuple
from screeninfo import Monitor


class PygameCalibrationGUI:
    """
    Pygame-based GUI for calibration procedure.
    Handles fullscreen display, point rendering, and user input.
    """
    
    # Colors
    BLACK = (0, 0, 0)
    WHITE = (255, 255, 255)
    RED = (255, 0, 0)
    GREEN = (0, 255, 0)
    BLUE = (0, 0, 255)
    GRAY = (128, 128, 128)
    
    # Frame colors for gaze quality feedback
    FRAME_OK_COLOR = GREEN
    FRAME_BAD_COLOR = RED
    FRAME_THICKNESS = 4
    
    def __init__(self, monitor: Monitor):
        """
        Initialize pygame calibration GUI.
        
        Args:
            monitor: Monitor object containing display information
        """
        self.monitor = monitor
        self.screen = None
        self.clock = None
        self.running = False
        self.win_name = 'Eye Tracking Calibration'
        
        # Initialize pygame
        pygame.init()
        pygame.display.init()
        
        # Set up fullscreen display on specified monitor
        self._setup_display()
        
    def _setup_display(self):
        """Set up fullscreen display on the specified monitor."""
        try:
            # Try to set display on specific monitor
            if hasattr(pygame.display, 'set_mode'):
                # Create fullscreen window
                self.screen = pygame.display.set_mode(
                    (self.monitor.width, self.monitor.height),
                    pygame.FULLSCREEN | pygame.NOFRAME
                )
                pygame.display.set_caption(self.win_name)
                
                # Move window to correct monitor position if possible
                import os
                os.environ['SDL_VIDEO_WINDOW_POS'] = f'{self.monitor.x},{self.monitor.y}'
                
        except Exception as e:
            # Fallback to default display
            self.screen = pygame.display.set_mode(
                (self.monitor.width, self.monitor.height),
                pygame.FULLSCREEN
            )
            
        self.clock = pygame.time.Clock()
        
    def get_window_name(self):
        return self.win_name
        
    def start(self):
        """Start the GUI."""
        self.running = True
        self.clear_screen()
        pygame.display.flip()
        
    def stop(self):
        """Stop the GUI and cleanup."""
        self.running = False
        pygame.display.quit()
        pygame.quit()
        
    def clear_screen(self, color=None):
        """Clear the screen with specified color."""
        if color is None:
            color = self.BLACK
        self.screen.fill(color)
        
    def draw_calibration_point(self, x: float, y: float, radius: int = 10, color=None):
        """
        Draw a calibration point at the specified position.

        Args:
            x: X coordinate (0-1 normalized)
            y: Y coordinate (0-1 normalized)
            radius: Point radius in pixels
            color: Point color tuple (R, G, B)
        """
        if color is None:
            color = self.WHITE

        # Convert normalized coordinates to screen coordinates
        screen_x = int(x * self.monitor.width)
        screen_y = int(y * self.monitor.height)

        # Draw outer circle
        pygame.draw.circle(self.screen, color, (screen_x, screen_y), radius)
        # Draw inner circle for better visibility
        pygame.draw.circle(self.screen, self.BLACK, (screen_x, screen_y), radius // 3)

    def draw_shrinking_stimulus(self, x: float, y: float, progress: float, max_radius: int = 30, color=None):
        """
        Draw a shrinking stimulus circle that reduces in size over time.

        Args:
            x: X coordinate (0-1 normalized)
            y: Y coordinate (0-1 normalized)
            progress: Progress from 0.0 (start) to 1.0 (end)
            max_radius: Maximum radius at start
            color: Circle color tuple (R, G, B)
        """
        if color is None:
            color = self.WHITE

        # Convert normalized coordinates to screen coordinates
        screen_x = int(x * self.monitor.width)
        screen_y = int(y * self.monitor.height)

        # Calculate current radius based on progress (shrinks from max to min)
        min_radius = 5
        current_radius = int(max_radius * (1.0 - progress) + min_radius * progress)

        # Draw the shrinking circle
        pygame.draw.circle(self.screen, color, (screen_x, screen_y), current_radius)
        # Draw center dot for fixation
        # pygame.draw.circle(self.screen, self.BLACK, (screen_x, screen_y), 2)
        
    def draw_frame_border(self, color, thickness=None):
        """
        Draw a colored border around the screen to indicate gaze quality.
        
        Args:
            color: Border color tuple (R, G, B)
            thickness: Border thickness in pixels
        """
        if thickness is None:
            thickness = self.FRAME_THICKNESS
            
        # Draw border rectangles
        width, height = self.monitor.width, self.monitor.height
        
        # Top border
        pygame.draw.rect(self.screen, color, (0, 0, width, thickness))
        # Bottom border  
        pygame.draw.rect(self.screen, color, (0, height - thickness, width, thickness))
        # Left border
        pygame.draw.rect(self.screen, color, (0, 0, thickness, height))
        # Right border
        pygame.draw.rect(self.screen, color, (width - thickness, 0, thickness, height))
        
    def draw_text(self, text: str, x: float, y: float, font_size: int = 36, color=None):
        """
        Draw text at the specified position.
        
        Args:
            text: Text to display
            x: X coordinate (0-1 normalized)
            y: Y coordinate (0-1 normalized)
            font_size: Font size in pixels
            color: Text color tuple (R, G, B)
        """
        if color is None:
            color = self.WHITE
            
        font = pygame.font.Font(None, font_size)
        text_surface = font.render(text, True, color)
        text_rect = text_surface.get_rect()
        
        # Convert normalized coordinates to screen coordinates
        screen_x = int(x * self.monitor.width)
        screen_y = int(y * self.monitor.height)
        
        # Center text at position
        text_rect.center = (screen_x, screen_y)
        self.screen.blit(text_surface, text_rect)
        
    def update_display(self):
        """Update the display."""
        pygame.display.flip()
        
    def handle_events(self) -> Optional[str]:
        """
        Handle pygame events and return command if any.
        
        Returns:
            'abort' if ESC pressed, 'start' if SPACE pressed, None otherwise
        """
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                return 'abort'
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    return 'abort'
                elif event.key == pygame.K_SPACE:
                    return 'start'
        return None
        
    def wait_for_space_or_escape(self) -> str:
        """
        Wait for user to press SPACE or ESC.
        
        Returns:
            'start' if SPACE pressed, 'abort' if ESC pressed
        """
        while self.running:
            result = self.handle_events()
            if result in ['start', 'abort']:
                return result
            self.clock.tick(60)  # Limit to 60 FPS
            
    def tick(self, fps: int = 60):
        """Tick the clock to maintain frame rate."""
        self.clock.tick(fps)
