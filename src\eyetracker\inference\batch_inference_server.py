#src/eyetracker/inference_process.py
"""
Inference Process Module
========================

This module defines the `InferenceProcess` class, which manages the inference operations
for an eye-tracking system. It handles the initialization and execution of different
neural network models (detector, iris segmentator, and pupil segmentator) using either
OpenVINO or ONNX runtime based on the configuration. The class facilitates inter-process
communication through multiprocessing queues to receive input data and send inference results.

Key Features:
- Supports multiple neural network types for eye detection and segmentation.
- Utilizes either OpenVINO or ONNX runtime for model inference based on system configuration.
- Manages inter-process communication using multiprocessing queues.
- Initializes models and performs initial inference on dummy data to ensure readiness.

Dependencies:
- `config`: Module containing configuration settings.
- `inference.InferenceOV`: Module for OpenVINO-based inference.
- `inference.InferenceOnnx`: Module for ONNX-based inference.
- Standard Python libraries: multiprocessing, logging, enum, typing, os, numpy.

Classes:
- `NNType`: Enumeration of supported neural network types.
- `InferenceProcess`: Manages the lifecycle and execution of inference models.

"""
from src.common.config import Conf
from src.eyetracker.inference import inference

import multiprocessing as mp
import numpy as np
import logging
from enum import Enum
import time

class NNType(Enum):
    """
    Enumeration of supported Neural Network types for inference.

    Attributes:
        MEDIAPIPE (int): Represents mediapipe face landmarks.
        DETECTOR (int): Represents the eye detector neural network.
        SEG_IRIS (int): Represents the iris segmentation neural network.
        SEG_PUPIL (int): Represents the pupil segmentation neural network.
    """
    MEDIAPIPE = 0
    DETECTOR = 1
    SEG_IRIS = 2
    SEG_PUPIL = 3


class InferenceProcess:
    """
    Manages the inference operations for the eye-tracking system.

    This class initializes and runs different neural network models for eye detection
    and segmentation. It uses multiprocessing queues to receive input data and send
    inference results to other processes.

    Attributes:
        q_in (mp.Queue): Queue for receiving inference requests.
        q_out (mp.Queue): Queue for sending inference results.
        conf (Conf): Configuration object containing system settings.
        detector (InferenceOV or InferenceOnnx): Neural network model for eye detection.
        iris_segmentator (InferenceOV or InferenceOnnx): Neural network model for iris segmentation.
        pupil_segmentator (InferenceOV or InferenceOnnx): Neural network model for pupil segmentation.
        n_points (int): Number of calibration points.
    """

    def __init__(self, q_in: mp.Queue, q_out: mp.Queue, conf: Conf):
        """
          Initializes the InferenceProcess with specified queues and configuration.

          Args:
              q_in (mp.Queue): Queue for receiving inference requests.
              q_out (mp.Queue): Queue for sending inference results.
              conf (Conf): Configuration object containing system settings.
          """

        self.conf = conf
        t1 = time.time()

        # initialize mediapipe model
        self.mediapipe = None
        if conf['use_mediapipe']:
            self.mediapipe = inference.MediaPipeDetector(conf)

        # Initialize the eye detector model
        self.detector = None
        if conf['use_old_detector']:
            self.detector = inference.Detector(conf)

        # Initialize the iris segmentator model
        self.iris_segmentator = inference.IrisSegmentator(conf)
        # prec = 'fp16'
        # Initialize the pupil segmentator model
        self.pupil_segmentator = inference.PupilSegmentator(conf)
        t2 = time.time()
        logging.info(f'Init inference time: {t2-t1:.3f}s')
        self.iris_images = []
        self.pupil_images = []
        # self.q_in = q_in    # Input queue for inference requests
        # self.q_out = q_out  # Output queue for inference results

    def inference(self, img, nn_type):
        """
        Performs inference using the specified neural network type.

        Args:
            img (np.ndarray): Input image or data for inference.
            nn_type (NNType): Type of neural network to use for inference.

        Returns:
            Any: Output from the inference model.
        """
        out = None
        if nn_type == NNType.MEDIAPIPE:
            assert self.mediapipe is not None
            out = self.mediapipe.inference(img)
        elif nn_type == NNType.DETECTOR:
            assert self.detector is not None
            # Perform inference using the detector model
            # assert img.shape == [1, 1, HEIGHT_DET, WIDTH_DET]
            out = self.detector.inference([img])
        else:
            # Perform inference using the appropriate segmentation model
            # assert img.shape == [BATCH_SEG, 1, HEIGHT_SEG, WIDTH_SEG]
            if nn_type == NNType.SEG_IRIS:
                self.iris_images.append(img)
                if len(self.iris_images) == 2:
                    try:
                        out = self.iris_segmentator.inference(self.iris_images)
                        self.iris_images = []
                    except:
                        for img in self.iris_images:
                            if type(img[0]) == np.ndarray:
                                print(img[0].shape)
                            else:
                                print(img)
                else:
                    out = None
            elif nn_type == NNType.SEG_PUPIL:
                self.pupil_images.append(img)
                if len(self.pupil_images) == self.conf['pupil_batch']:
                    out = self.pupil_segmentator.inference(self.pupil_images)
                    self.pupil_images = []
                else:
                    out = None
            else:
                logging.error('Unknown NN type passed')
        return out
        
    # def run(self):
    #     """
    #     Starts the inference process loop.
    #
    #     Continuously listens for inference requests from the input queue, processes them,
    #     and sends the results to the output queue.
    #     """
    #     logging.info(f'Inference proc started, PID:{os.getpid()}')
    #     while True:
    #         # Retrieve inference request from the input queue with a timeout
    #         try:
    #             inputs: T.Tuple[np.array, NNType, int, T.Any] = self.q_in.get(timeout=1.)
    #         except:
    #             continue
    #         img, nn_type, frame_id, frame_info = inputs # Unpack the input tuple
    #         out = self.inference(img, nn_type)   # Perform inference
    #         if out is not None:
    #             # Send the inference result to the output queue
    #             self.q_out.put((out, nn_type, frame_id, frame_info))
