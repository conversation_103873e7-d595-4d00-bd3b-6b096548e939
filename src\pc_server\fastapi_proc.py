from fastapi import FastAP<PERSON>, Response
from fastapi.responses import StreamingResponse, PlainTextResponse
import uvicorn
# import multiprocessing as mp
from queue import Queue
import typing as T
import os
import logging
import time

app = FastAPI()


class FastapiProc:
    def __init__(self,
                 q_frames: Queue,
                 q_send_commands: Queue,
                 q_receive_commands: Queue,
                 q_records: Queue):
        self.q_frames = q_frames
        self.q_send_commands = q_send_commands
        self.q_receive_commands = q_receive_commands
        self.q_records = q_records
        self.records = []
        self.last_record = 0
        self.app = app
        self.busy = False
        self.log = logging.getLogger('api_logger')
        file_handler = logging.FileHandler("api.log")
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(logging.Formatter("%(asctime)s - [%(levelname)s] -  %(name)s - %(message)s"))
        self.log.handlers.clear()
        self.log.addHandler(file_handler)

    def clear_queues(self):
        while self.q_records.qsize()>0:
            self.q_records.get()
        self.records = []
        # with self.q_records.mutex:
        #     self.q_records.queue.clear()
    
    # app.get('/records')
    def get_records(self, n_records: T.Optional[int] = None, ack_number: T.Optional[int] = None):
        if ack_number is not None:
            while len(self.records) > 0 and self.records[0][0] <= ack_number:
                self.records.pop(0)
        while self.q_records.qsize() > 0:
            rec: T.Tuple[int, bytes] = self.q_records.get()
            self.records.append(rec)
        result = b''
        n = len(self.records)
        if n_records is not None and n_records < n:
            n = n_records
        for i in range(n):
            # self.log.info(f'{str(self.records[i][1])} {ack_number=} queue length = {len(self.records)}')
            result += self.records[i][1]
            result += b'\n\r'
        return PlainTextResponse(result)

    # app.post('/commands')
    def commands(self, command:str, timeout=2.):
        logging.info(f"Command received: {command}")
        t1 = time.time()
        if 'ENABLE_SEND_DATA' in command:
            self.clear_queues()
        while self.busy:
            time.sleep(0.01)
            t = time.time()
            if t - t1 > timeout:
                return PlainTextResponse('Server is busy', 504)
        while self.q_receive_commands.qsize() > 0:
            self.q_receive_commands.get()
        self.busy = True
        self.q_receive_commands.put(command)
        try:
            resp = self.q_send_commands.get(timeout=timeout)
        except:
            self.busy = False
            return PlainTextResponse('Gateway timeout, possibly incorrect request', 504)
        logging.info(f"Response: {resp}")
        self.busy = False
        return PlainTextResponse(resp)

    # app.get('/video_feed')
    def video_feed(self):
        return StreamingResponse(self.gen_video(), media_type='multipart/x-mixed-replace; boundary=frame')

    def gen_video(self):
        while True:
            try:
                frame: bytes = self.q_frames.get(timeout=1.)
            except:
                continue
            yield (b'--frame\r\n'
                   b'Content-Type: image/jpeg\r\n\r\n' + frame + b'\r\n')  # concat frame one by one and show result

    def run(self):
        # logging.info(f'Fastapi proc started, PID:{os.getpid()}')
        self.app.add_api_route(path='/video_feed', endpoint=self.video_feed,
                               methods=['GET'], response_class=StreamingResponse)
        self.app.add_api_route(path='/records', endpoint=self.get_records,
                               methods=['GET'], response_class=PlainTextResponse)
        self.app.add_api_route(path='/commands', endpoint=self.commands,
                               methods=['POST'], response_class=PlainTextResponse)
        uvicorn.run(self.app, port=4242)
