"""
Test script for the unified calibration procedure.
Tests the new pygame-based calibration system.
"""

import sys
import os
import time
import numpy as np
from queue import Queue
from unittest.mock import Mock

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.common.eye_data import CalibrPoint, FrameData, EyeData
from src.common.config import Conf, default_conf
from screeninfo import Monitor, get_monitors
from src.pc_server.unified_calibration_procedure import UnifiedCalibrationProcedure


def create_mock_frame_data(gaze_quality='good'):
    """Create mock frame data for testing."""
    frame_data = FrameData()
    frame_data.timestamp = time.time()
    frame_data.ticks = int(time.time() * 1000)
    
    for i in range(2):  # left and right eye
        eye_data = frame_data.eye_data[i]
        
        if gaze_quality == 'good':
            # Good quality data
            eye_data.eye_center = np.array([100.0 + i * 50, 150.0])
            eye_data.glints = np.array([[90.0 + i * 50, 140.0], [110.0 + i * 50, 160.0]])
            eye_data.gaze_origin = np.array([0.0 + i * 30, 0.0, 600.0])
            eye_data.gaze_vec = np.array([0.1 + i * 0.1, 0.05, -0.99])
            # Normalize gaze vector
            eye_data.gaze_vec = eye_data.gaze_vec / np.linalg.norm(eye_data.gaze_vec)
        else:
            # Bad quality data (NaN values)
            eye_data.eye_center = np.full(2, np.nan)
            eye_data.glints = np.full((2, 2), np.nan)
            eye_data.gaze_origin = np.full(3, np.nan)
            eye_data.gaze_vec = np.full(3, np.nan)
            
    return frame_data


def create_test_calibration_points():
    """Create test calibration points."""
    points = []
    # Create a 3x3 grid of calibration points
    for x in [0.2, 0.5, 0.8]:
        for y in [0.2, 0.5, 0.8]:
            point = CalibrPoint()
            point.pog = [x, y]
            points.append(point)
    return points


def create_test_config():
    """Create test configuration."""
    config = default_conf
    config['fps'] = 60
    config['calibr_duration'] = 2.0
    config['calibr_duration_ratio'] = 0.8
    config['calibr_sin_thr'] = 0.1
    config['calibr_validation_threshold'] = 0.05
    config['send_deep_diagnostics'] = 'never'
    config['session_data_dir'] = './test_data/'
    config['debug'] = True
    return config

def create_test_monitor():
    """Create test monitor."""
    # monitor = Monitor(0, 0, 1920, 1080)
    # monitor.width_mm = 476
    # monitor.height_mm = 268
    monitor = get_monitors()[0]
    return monitor


def test_pygame_gui():
    """Test pygame GUI initialization and basic functionality."""
    print("Testing pygame GUI...")

    try:
        from src.pc_server.pygame_calibration_gui import PygameCalibrationGUI

        monitor = create_test_monitor()
        gui = PygameCalibrationGUI(monitor)

        # Test basic operations
        gui.start()
        gui.clear_screen()
        gui.draw_calibration_point(0.5, 0.5)
        gui.draw_text("Test", 0.5, 0.1)

        # Test shrinking stimulus at different progress levels
        for progress in [0.0, 0.5, 1.0]:
            gui.clear_screen()
            gui.draw_shrinking_stimulus(0.5, 0.5, progress)
            gui.update_display()
            time.sleep(0.2)

        gui.stop()
        print("✓ Pygame GUI test passed")
        return True

    except Exception as e:
        print(f"✗ Pygame GUI test failed: {e}")
        return False


def test_data_averaging():
    """Test data averaging functionality."""
    print("Testing data averaging...")
    
    try:
        # Create test data
        frame_data_list = []
        for i in range(10):
            frame_data = create_mock_frame_data('good')
            # Add some variation
            for eye_idx in range(2):
                frame_data.eye_data[eye_idx].eye_center += np.random.normal(0, 1, 2)
                frame_data.eye_data[eye_idx].gaze_vec += np.random.normal(0, 0.01, 3)
                # Renormalize
                norm = np.linalg.norm(frame_data.eye_data[eye_idx].gaze_vec)
                frame_data.eye_data[eye_idx].gaze_vec /= norm
            frame_data_list.append(frame_data)
            
        # Create calibration procedure instance
        monitor = create_test_monitor()
        config = create_test_config()
        cal_points = create_test_calibration_points()[:1]  # Just one point for testing
        
        cal_proc = UnifiedCalibrationProcedure(
            cal_points, Queue(), Queue(), Queue(), config, monitor
        )
        
        # Test averaging
        averaged_data = cal_proc.average_eye_data(frame_data_list)
        
        # Verify results
        assert 'left' in averaged_data and 'right' in averaged_data
        assert not np.isnan(averaged_data['left'].eye_center).any()
        assert not np.isnan(averaged_data['left'].gaze_vec).any()
        assert abs(np.linalg.norm(averaged_data['left'].gaze_vec) - 1.0) < 0.001
        
        print("✓ Data averaging test passed")
        return True
        
    except Exception as e:
        print(f"✗ Data averaging test failed: {e}")
        return False


def test_calibration_point_processing():
    """Test calibration point processing."""
    print("Testing calibration point processing...")

    try:
        monitor = create_test_monitor()
        config = create_test_config()
        cal_points = create_test_calibration_points()[:1]

        cal_proc = UnifiedCalibrationProcedure(
            cal_points, Queue(), Queue(), Queue(), config, monitor
        )

        # Create test data with new structure
        test_data = []
        frame_data_list = []
        for i in range(20):
            # Create frame data
            frame_data = create_mock_frame_data('good')
            frame_data_list.append(frame_data)

            # Create data point structure
            data_point = {
                'gaze_data': cal_proc.numpy_gaze(frame_data),
                'frame_data': frame_data,
                'timestamp': 0.5 + i * 0.05  # Valid timestamps within window
            }
            test_data.append(data_point)

        cal_proc.current_point_data = test_data
        cal_proc.all_frame_data = [frame_data_list]

        # Test point averaging (no more grouping)
        updated_point = cal_proc.get_averaged_calibration_point(cal_points[0], 0)
        assert updated_point is not None
        assert updated_point.pog == cal_points[0].pog

        print("✓ Calibration point processing test passed")
        return True

    except Exception as e:
        print(f"✗ Calibration point processing test failed: {e}")
        return False


def run_all_tests():
    """Run all tests."""
    print("Running unified calibration tests...\n")
    
    tests = [
        test_pygame_gui,
        test_data_averaging,
        test_calibration_point_processing
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
        
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed!")
        return True
    else:
        print("❌ Some tests failed")
        return False


if __name__ == "__main__":
    # Create test data directory
    os.makedirs('./test_data/deep_diag', exist_ok=True)
    
    success = run_all_tests()
    sys.exit(0 if success else 1)
