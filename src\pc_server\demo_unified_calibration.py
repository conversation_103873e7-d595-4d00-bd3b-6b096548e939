"""
Demo script for the unified calibration procedure.
Shows the new pygame-based calibration with shrinking stimulus.
"""

import sys
import os
import time
import numpy as np
from queue import Queue
import threading

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.common.eye_data import CalibrPoint, FrameData, EyeData
from src.common.config import Conf, default_conf
from screeninfo import Monitor
from src.pc_server.unified_calibration_procedure import UnifiedCalibrationProcedure


def create_demo_config():
    """Create demo configuration."""
    config = default_conf.copy()
    # config = {
    #     'fps': 60,
    #     'calibr_duration': 2.0,
    #     'calibr_duration_ratio': 0.8,
    #     'calibr_sin_thr': 0.1,
    #     'calibr_validation_threshold': 0.05,
    #     'send_deep_diagnostics': 'never',
    #     'session_data_dir': './demo_data/',
    #     'debug': True
    # }
    return Conf(config) 


def create_demo_monitor():
    """Create demo monitor using primary display."""
    try:
        import screeninfo
        monitors = screeninfo.get_monitors()
        if monitors:
            primary = monitors[0]
            # monitor = Monitor()
            # monitor.width = primary.width
            # monitor.height = primary.height
            # monitor.x = primary.x
            # monitor.y = primary.y
            return primary
    except:
        pass
    
    # Fallback monitor
    monitor = Monitor()
    monitor.width = 1920
    monitor.height = 1080
    monitor.x = 0
    monitor.y = 0
    return monitor


def create_demo_calibration_points():
    """Create demo calibration points (3x3 grid)."""
    points = []
    for y in [0.1, 0.5, 0.9]:
        for x in [0.1, 0.5, 0.9]:
            point = CalibrPoint()
            point.pog = [x, y]
            points.append(point)
    return points


def simulate_eye_tracking_data(q_frame_data: Queue, running_flag: threading.Event):
    """Simulate eye tracking data for demo purposes."""
    frame_id = 0
    
    while running_flag.is_set():
        # Create mock frame data
        frame_data = FrameData()
        frame_data.timestamp = time.time()
        frame_data.ticks = int(time.time() * 1000)
        
        # Simulate good quality eye tracking data
        for i in range(2):  # left and right eye
            eye_data = frame_data.eye_data[i]
            
            # Add some realistic noise
            base_center = np.array([100.0 + i * 50, 150.0])
            noise = np.random.normal(0, 2, 2)
            eye_data.eye_center = base_center + noise
            
            # Glints with noise
            base_glints = np.array([[90.0 + i * 50, 140.0], [110.0 + i * 50, 160.0]])
            glint_noise = np.random.normal(0, 1, (2, 2))
            eye_data.glints = base_glints + glint_noise
            
            # Gaze data with slight variation
            eye_data.gaze_origin = np.array([0.0 + i * 30, 0.0, 600.0]) + np.random.normal(0, 5, 3)
            base_gaze_vec = np.array([0.1 + i * 0.1, 0.05, -0.99])
            gaze_noise = np.random.normal(0, 0.01, 3)
            eye_data.gaze_vec = base_gaze_vec + gaze_noise
            # Normalize
            eye_data.gaze_vec = eye_data.gaze_vec / np.linalg.norm(eye_data.gaze_vec)
            
        # Send frame data
        try:
            q_frame_data.put(frame_data, timeout=0.1)
        except:
            pass  # Queue full, skip this frame
            
        frame_id += 1
        time.sleep(1.0 / 60)  # 60 FPS simulation


def run_demo():
    """Run the calibration demo."""
    print("Starting unified calibration demo...")
    print("This will show the new pygame-based calibration with shrinking stimulus.")
    print("Press SPACE to start each calibration point, ESC to abort.")
    print()
    
    # Create demo data directory
    os.makedirs('./demo_data/deep_diag', exist_ok=True)
    
    # Setup
    config = create_demo_config()
    monitor = create_demo_monitor()
    cal_points = create_demo_calibration_points()
    
    # Create queues
    q_frame_data = Queue(maxsize=100)
    q_commands = Queue()
    q_data = Queue()
    
    # Start eye tracking simulation
    running_flag = threading.Event()
    running_flag.set()
    
    sim_thread = threading.Thread(
        target=simulate_eye_tracking_data,
        args=(q_frame_data, running_flag),
        daemon=True
    )
    sim_thread.start()
    
    try:
        # Create and run calibration procedure
        cal_proc = UnifiedCalibrationProcedure(
            cal_points, q_frame_data, q_commands, q_data, config, monitor
        )
        
        print(f"Starting calibration with {len(cal_points)} points...")
        result = cal_proc.run()
        
        if result is not None:
            print(f"\n✓ Calibration completed successfully!")
            print(f"Calibrated {len(result)} points")
            
            # Show results
            for i, point in enumerate(result):
                left_eye = point.eye_data[0]
                right_eye = point.eye_data[1]
                print(f"Point {i+1}: POG=({point.pog[0]:.2f}, {point.pog[1]:.2f})")
                if not np.isnan(left_eye.gaze_vec).any():
                    print(f"  Left gaze: ({left_eye.gaze_vec[0]:.3f}, {left_eye.gaze_vec[1]:.3f}, {left_eye.gaze_vec[2]:.3f})")
                if not np.isnan(right_eye.gaze_vec).any():
                    print(f"  Right gaze: ({right_eye.gaze_vec[0]:.3f}, {right_eye.gaze_vec[1]:.3f}, {right_eye.gaze_vec[2]:.3f})")
        else:
            print("\n❌ Calibration was aborted or failed")
            
    except KeyboardInterrupt:
        print("\n⚠️ Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # Stop simulation
        running_flag.clear()
        if sim_thread.is_alive():
            sim_thread.join(timeout=1)
        print("\nDemo finished.")


if __name__ == "__main__":
    run_demo()
