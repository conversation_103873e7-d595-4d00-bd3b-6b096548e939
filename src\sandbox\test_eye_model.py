import numpy as np
from scipy.optimize import minimize


def normalized(v):
    return v / np.linalg.norm(v)


def calculate_q(kq, o, u):
    return o + kq * normalized(o - u)


def calculate_cornea_center(q, light, camera_position, R):
    l_q_unit = normalized(light - q)
    o_q_unit = normalized(camera_position - q)
    return q - R * normalized(l_q_unit + o_q_unit)


def distance_between_corneas(kqs, glints, lights, camera_position, R):
    cs = [calculate_cornea_center(calculate_q(kq, camera_position, glint), light, camera_position, R)
          for kq, glint, light in zip(kqs, glints, lights)]
    residuals = []
    for i in range(len(cs)):
        for j in range(i):
            d = cs[i] - cs[j]
            residuals.extend(d)
    return np.array(residuals)


def calculate_cornea_center_wcs(glints, lights, camera_position, R, camera_eye_distance_estimate):
    initial_kqs = np.full(len(glints), camera_eye_distance_estimate)
    bounds = [(2, 400)] * len(glints)

    result = minimize(lambda kqs: np.linalg.norm(distance_between_corneas(kqs, glints, lights, camera_position, R)),
                      initial_kqs, bounds=bounds, method='L-BFGS-B')

    if not result.success:
        raise RuntimeError("Optimization failed")

    kqs_opt = result.x
    c_total = sum(calculate_cornea_center(calculate_q(kq, camera_position, glint), light, camera_position, R)
                  for kq, glint, light in zip(kqs_opt, glints, lights))
    return c_total / len(glints)


# Sample usage
glints = [np.array([-0.05, 0, -16]), np.array([0.05, 0, -16])]
lights = [np.array([-120, 0, 0]), np.array([120, 0, 0])]
camera_position = np.array([0, 0, 0])
R = 7.8  # Example corneal radius
camera_eye_distance_estimate = 10  # Initial estimate

cornea_center = calculate_cornea_center_wcs(glints, lights, camera_position, R, camera_eye_distance_estimate)
print("Estimated Cornea Center:", cornea_center)

##################
import numpy as np


def normalized(v):
    return v / np.linalg.norm(v)


def calculate_iota(camera_position, pupil_por_wcs, center_of_cornea, R, n1, n2):
    zeta = normalized(camera_position - pupil_por_wcs)
    eta = (pupil_por_wcs - center_of_cornea) / R
    eta_dot_zeta = np.dot(eta, zeta)

    a = eta_dot_zeta - np.sqrt((n1 / n2) ** 2 - 1 + eta_dot_zeta ** 2)
    return (n2 / n1) * (a * eta - zeta)


def calculate_kr(camera_position, image_pupil_center, cornea_center, R):
    a = np.dot(camera_position - image_pupil_center, camera_position - image_pupil_center)
    b = np.dot(camera_position - image_pupil_center, camera_position - cornea_center)
    c = np.dot(camera_position - cornea_center, camera_position - cornea_center) - R ** 2

    return (-b - np.sqrt(b ** 2 - a * c)) / a


def calculate_r(camera_position, pupil_image_wcs, cornea_wcs, R):
    kr = calculate_kr(camera_position, pupil_image_wcs, cornea_wcs, R)
    return camera_position + kr * (camera_position - pupil_image_wcs)


def calculate_p(camera_position, pupil_por_wcs, center_of_cornea, R, K, n1, n2):
    iota = calculate_iota(camera_position, pupil_por_wcs, center_of_cornea, R, n1, n2)
    rc_dot_iota = np.dot(pupil_por_wcs - center_of_cornea, iota)
    kp = -rc_dot_iota - np.sqrt(rc_dot_iota ** 2 - (R ** 2 - K ** 2))
    return pupil_por_wcs + kp * iota


def calculate_optic_axis_unit_vector(pupil_wcs, camera_position, center_of_cornea, R, K, n1, n2,
                                     use_chen_noise_reduction):
    pupil_por_wcs = calculate_r(camera_position, pupil_wcs, center_of_cornea, R)
    pupil_center_wcs = calculate_p(camera_position, pupil_por_wcs, center_of_cornea, R, K, n1, n2)

    if use_chen_noise_reduction:
        cxpx = center_of_cornea[0] - pupil_center_wcs[0]
        cypy = center_of_cornea[1] - pupil_center_wcs[1]
        pupil_center_wcs[2] = center_of_cornea[2] - np.sqrt(K ** 2 - cxpx ** 2 - cypy ** 2)

    return normalized(pupil_center_wcs - center_of_cornea)
