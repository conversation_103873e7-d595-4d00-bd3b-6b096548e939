@REM rem call C:/eyetracker/usb-openvino/.venv/Scripts/activate.bat
@REM rem conda activate eyetracker

python src/common/version.py ./src/eyetracker
pyinstaller ^
--add-data "weights/test:weights/release" --add-data "weights/v3:weights/v3" --add-data "version.txt:version.txt" ^
--paths ./;./src --onefile ./src/eyetracker/app.py ^
--hidden-import scipy._lib.array_api_compat.numpy.fft ^
--add-data C:/Users/<USER>/anaconda3/envs/eyetracker310/Lib/site-packages/mediapipe/modules/:./mediapipe/modules/ ^
--version-file ./src/eyetracker/version_info.py
@REM --add-data "../TensorRT-*********/lib:./" ^

@REM --add-binary C:/Users/<USER>/anaconda3/envs/eyetracker/Lib/site-packages/onnxruntime/capi/onnxruntime_providers_cuda.dll:./onnxruntime/capi/ ^

@REM rem --add-binary .venv/Lib/site-packages/onnxruntime/capi/onnxruntime_providers_tensorrt.dll:./onnxruntime/capi/ ^
@REM rem pyinstaller --add-data "weights/v2:weights/v2" --onefile app.py
xcopy /y dist\app.exe .\eyetracker.exe
pause
