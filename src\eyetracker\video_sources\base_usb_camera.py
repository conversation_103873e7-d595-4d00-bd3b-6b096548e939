from src.eyetracker.video_sources.BaseVideoSource import BaseVideoSource
from src.common.config import Conf

from src.common.chrono import Chrono

import numpy as np
import cv2


class BaseUsbCamera(BaseVideoSource):
    def __init__(self, conf:Conf):
        super().__init__(conf)
        self.frame_index = 0
        self.under_light = False
        self.over_light = False
        self.max_exposure_time_us = min(1.E6 / self.conf['fps'] - 200., 2.E4)
        self.min_exposure_time_us = 100.
        self.min_linear_gain = 1.
        self.max_linear_gain = 8.
        self.exposure_time_us = self.max_exposure_time_us
        self.linear_gain = self.min_linear_gain
        self.target_mean = 128
        self.target_mean_tolerance = 5
        self.target_over = 0.05
        self.max_step = 1.25
        self.area = 128 * 96
        self.n_avg = max(int(self.fps/10), 10)
        self.means = []
        self.overs = []
        self.quantiles = []
        self.chrono1 = Chrono()
        self.chrono2 = Chrono()


    def get_frame(self):
        frame, timestamp = self.do_get_frame()
        if frame is not None:
            self.chrono1.start()
            frame_r = cv2.resize(frame, (128, 96), interpolation=cv2.INTER_NEAREST)
            # frame_r = frame[::4, ::4]
            self.means.append(np.mean(frame_r))
            self.overs.append(np.sum(frame_r > 250))
            self.quantiles.append(np.quantile(frame_r, q=1 - self.target_over))
            self.chrono1.stop()

            if len(self.means) >= self.n_avg:
                self.chrono2.start()
                # self.area = frame.shape[0] * frame.shape[1]
                mean = max(np.mean(self.means), 10.)
                over = np.mean(self.overs) / self.area
                quantile = max(np.mean(self.quantiles), 10.)
                # print(f"{mean=:.0f} {over=:.3f} {quantile=:.0f}")
                # (f"{mean=} {over=}")
                self.means = []
                self.overs = []
                if over > self.target_over:
                    self.correct_exposure(1 - (over - self.target_over))
                elif abs(mean - self.target_mean) > self.target_mean_tolerance:
                    # if mean < 10:
                    #     mean = 10
                    # if quantile < 10:
                    #     quantile = 10
                    # print(self.target_mean/mean, 250/quantile)
                    k = min(self.target_mean/mean, 250/quantile)
                    if k < 1 or over < self.target_over/2:
                        self.correct_exposure(k)
                self.chrono2.stop()
            self.frame_index += 1
        return frame, timestamp

    def do_get_frame(self):
        raise NotImplementedError('Not implemented')

    def set_values(self, values: dict):
        raise NotImplementedError('Not implemented')
    
    def correct_exposure(self, k):
        self.under_light = False
        self.over_light = False
        if k > self.max_step:
            k = self.max_step
        if k < 1 / self.max_step:
            k = 1/ self.max_step
        new_exp_time = self.exposure_time_us
        new_gain = self.linear_gain
        if k > 1: # first try to increase time
            new_exp_time *= k
            if new_exp_time > self.max_exposure_time_us:
                new_exp_time = self.max_exposure_time_us
                new_gain =  new_gain * k * self.exposure_time_us / new_exp_time
                if new_gain > self.max_linear_gain:
                    new_gain = self.max_linear_gain
                    self.under_light = True
        if k < 1: # first try to decrease gain
            new_gain *= k
            if new_gain < self.min_linear_gain:
                new_gain = self.min_linear_gain
                new_exp_time =  new_exp_time * k * self.linear_gain / new_gain
                if new_exp_time < self.min_exposure_time_us:
                    new_exp_time = self.min_exposure_time_us
                    self.over_light = True
        # print(f"{new_exp_time=} {new_gain=}")
        if new_exp_time != self.exposure_time_us:
            self.exposure_time_us = new_exp_time
            self.set_exposure_time()
        if new_gain != self.linear_gain:
            self.linear_gain = new_gain
            self.set_gain()
            
    def set_exposure_time(self, time=None):
        if time is None:
            time = self.exposure_time_us
        self.set_values({'ExposureTime': float(time)})
        
    def set_gain(self, gain=None):
        raise NotImplementedError('Not implemented')

    def _set_gain(self, gain=None, log=False):
        if gain is None:
            gain = self.linear_gain
        if log:
            self.set_values({'Gain':float(10*np.log10(gain))})
        else:
            self.set_values({'GainRaw': float(gain)})

    def end(self):
        return False
