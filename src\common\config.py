#src/eyetracker/config.py
"""
Eye Tracker Configuration Module
================================

This module defines the configuration settings and parameters for the eye-tracking system.
It includes constants related to video capture, neural network dimensions, camera configurations,
and various operational parameters. The module also provides a `Conf` typed dictionary for
managing configuration settings and a `calc_conf` function to compute derived configuration
values based on the initial settings.

Key Components:
- Configuration Constants: Define static parameters for video capture and neural network operations.
- `Conf` Typed Dictionary: Specifies the structure and types for configuration settings.
- `default_conf`: Provides default values for all configuration settings.
- `calc_conf` Function: Computes and updates configuration settings based on initial values.
- Main Section: Demonstrates how to modify and save custom configuration settings.

Dependencies:
- NumPy for numerical operations.
- datetime for timestamping session directories.
- typing for type annotations.
"""
import numpy as np
from datetime import datetime
import typing as T
    
# Video capture parameters
FRAME_WIDTH, FRAME_HEIGHT = (720, 540)  # (1280, 544)
#FRAME_WIDTH, FRAME_HEIGHT = 1440, 1080

# NN parameters

WIDTH_OUT_DET = 16
HEIGHT_OUT_DET = 12

WIDTH_DET = 8*WIDTH_OUT_DET
HEIGHT_DET = 8*HEIGHT_OUT_DET

EYE_DETECTION_THRESHOLD = 0.8

WIDTH_SEG = 64
HEIGHT_SEG = WIDTH_SEG

BATCH_SEG = 2

WIDTH_CROP = 96
HEIGHT_CROP = WIDTH_CROP

IRIS_SCALE_FOR_CROP_FACTOR = 1.1

# CLOUD_HOST = "https://web.neuroiconica.com:8080"
# CLOUD_PARAMS_URI = "/api/pathfinder_cloud/parameters/"
# CLOUD_UPLOAD_URI = "/api/pathfinder_cloud/analytics/"
# TOKEN_PATH = 'cloud.key'
# SETTINGS_PATH = 'cloud.settings'
# DATA_DIR = 'data'

SEND_DEEP_DIAGNOSTICS_VALUES = ['never', 'experiment', 'always']

def get_exposure_us(fps:float) -> float:
    PROCESSING_TIME = 200 # we should stay this time for processing frame
    MAX_EXPOSURE = 2E4 # 20ms
    return min(1E6/fps - PROCESSING_TIME, MAX_EXPOSURE)

class Conf(T.TypedDict):
    """
    Configuration Typed Dictionary for Eye Tracker.

    Defines all configuration settings required for the eye-tracking system, including
    hardware parameters, neural network settings, and operational flags.
    """
    debug: bool
    use_gpu: bool
    fps: float
    interp: int
    detect_fps: float
    iris_fps: float
    stream_fps: float
    record_fps: float
    pupil_batch: int
    cloud_host: str
    cloud_params_uri: str
    cloud_upload_uri: str
    cloud_token_path: str
    cloud_settings_path: str
    send_diagnostics_fpm: float
    send_diagnostics_to_cloud: bool
    send_deep_diagnostics: str
    max_full_frames_deep_diagnostics: int
    max_diagnostic_data_size: int
    min_free_space: int
    weights_version: str
    use_mediapipe: bool
    use_old_detector: bool
    use_socket: bool
    video_file: bool
    video_file_name: str
    video_file_sleep: float
    data_dir: str
    session_data_dir: str
    token: str
    enable_onnx_gpu: bool
    enable_onnx_trt: bool
    camera_type: str
    camera_focal_length_mm: float
    camera_pixel_size: float
    camera_binning: int
    camera_f: np.ndarray
    camera_cf: np.ndarray
    calibr_load_on_start: bool
    calibr_fixation_time: float
    calibr_sin_thr: float
    calibr_duration_ratio: float
    calibr_min_points: int
    calibr_correction_enabled: bool
    calibr_validation_threshold: float
    use_unified_calibration: bool  # Use new pygame-based unified calibration procedure
    clip_pog_data: bool
    eye_distance: float
    eye_R: float
    eye_Dr: float
    led_coords: np.array
    frames_queue_len: int
    fix_glare: bool
    filter_bad_gaze: bool
    filter_bad_gaze_thr: float
    usb_camera_config: dict[str, T.Any]
    leo_camera_config: dict[str, T.Any]
    mars_camera_config: dict[str, T.Any]
    default_eye_crop_size: int  # Default size for eye crop region when iris size is unknown


default_conf: Conf = {
    'debug': False,
    'use_gpu': True,
    'fps': 100,
    'detect_fps': 25,
    'iris_fps': 50,
    'stream_fps': 10,
    'record_fps': 0,
    'pupil_batch': 8,
    'cloud_host': 'http://web.neuroiconica.com:8080',
    'cloud_params_uri': '/api/pathfinder_cloud/parameters/',
    'cloud_upload_uri': '/api/pathfinder_cloud/analytics/',
    'cloud_token_path': 'cloud.key',
    'cloud_settings_path': 'cloud.settings',
    'send_diagnostics_fpm': 0,
    'send_diagnostics_to_cloud': True,
    'send_deep_diagnostics': 'never',
    'max_full_frames_deep_diagnostics': 100,
    'max_diagnostic_data_size': 10 * (1024**3), # 10 Gb
    'min_free_space': 10 * (1024**3), # 10 Gb
    'weights_version': 'release',
    'use_mediapipe': True,
    'use_old_detector': True,
    'use_socket': False,
    'video_file': False,
    'video_file_name': 'sample.avi',
    'video_file_sleep': 0.01,
    'data_dir': 'data',
    'enable_onnx_gpu': True,
    'enable_onnx_trt': False,
    'camera_type': 'LEO',
    'camera_focal_length_mm': 16,
    'camera_pixel_size': 3.44E-3,
    'camera_binning': 2,
    'calibr_load_on_start': False,
    'calibr_fixation_time': 0.15,
    'calibr_sin_thr': 0.02,
    'calibr_duration_ratio': 1.2,
    'calibr_min_points': 10,
    'calibr_correction_enabled': True,
    'calibr_validation_threshold': 0.1,
    'use_unified_calibration': True,  # Enable new pygame-based calibration by default
    'clip_pog_data': True,
    'eye_distance': 60.,
    'eye_R': 6.8,
    'eye_Dr': 3.81,
    'led_coords': np.array([120., 0., 0.]),
    'frames_queue_len': 16,
    'fix_glare': True,
    'filter_bad_gaze': True,
    'filter_bad_gaze_thr': 0.9,
    'usb_camera_config': {
        'Width': FRAME_WIDTH,
        'Height': FRAME_HEIGHT,
        'ReverseX': True,
        # 'BlackLevelEnable': True,
        # 'BlackLevelAuto': True,
        'AcquisitionFrameRate': 100.,
        'AcquisitionFrameRateEnable': True,
        # 'ExposureTime':	1800.,  # float(min(1E6/FPS - 200, 10000)),
        # 'GainRaw': 16.,
        'ExposureAuto': 'Off',
    },
    'leo_camera_config': {
        # 'AutoGainUpperLimit': 12.,
        'GainAuto': 'Off',
        'ADCBitDepth': 'Bits_8',
        # 'AutoExposureTimeUpperLimit': int(get_exposure_us(100.)),
        'Gain': 6.,
    },
    'mars_camera_config': {
        'GainRaw': 4.,
    },
    'default_eye_crop_size': 96,  # Default value from previous WIDTH_CROP constant
}

# PIXEL_SIZE = 3.44E-3 * 2
# FOCAL_LENGTH = 16
# F = np.array([FOCAL_LENGTH/PIXEL_SIZE]*2)
# CF = np.array([FRAME_WIDTH/2, FRAME_HEIGHT/2])


def calc_conf(conf: Conf) -> Conf:
    """
    Calculates and updates derived configuration settings based on initial values.

    Args:
        conf (Conf): The initial configuration settings.

    Returns:
        Conf: The updated configuration settings with derived values computed.
    """
    if not conf['use_old_detector'] and not conf['use_mediapipe']:
        conf['use_mediapipe'] = True
    conf['session_data_dir'] = f'{conf["data_dir"]}/{datetime.now().strftime("%Y_%m_%d-%H-%M-%S")}/'
    if conf['send_deep_diagnostics'] not in SEND_DEEP_DIAGNOSTICS_VALUES:
        conf['send_deep_diagnostics'] = SEND_DEEP_DIAGNOSTICS_VALUES[0]
    conf['camera_f'] = np.array([conf['camera_focal_length_mm']/conf['camera_pixel_size']/conf['camera_binning']]*2)
    conf['camera_cf'] = np.array([FRAME_WIDTH/2, FRAME_HEIGHT/2])
    if conf['fps'] > 250:
        conf['interp'] = 1
        conf['pupil_batch'] = 8
    else:
        conf['interp'] = 0
    conf['usb_camera_config']['AcquisitionFrameRate'] = float(conf['fps']/(conf['interp']+1))
    conf['leo_camera_config']['AutoExposureTimeUpperLimit'] = int(get_exposure_us(conf['fps']))
    if conf['camera_type'].capitalize() == 'Mars':
        conf['usb_camera_config'] |= conf['mars_camera_config']
    else:
        conf['usb_camera_config'] |= conf['leo_camera_config']
    return conf


def generate_config():
    import json
    import shutil
    import os
    custom_conf: Conf = {
        'send_deep_diagnostics': 'never',
        'send_diagnostics_fpm': 60,
        'filter_bad_gaze': True,
        'filter_bad_gaze_thr': 0.98,
        'calibr_fixation_time': 0.15,
        'calibr_sin_thr': 0.02,
        'calibr_duration_ratio': 2.,
        'calibr_min_points': 10,
    }
    fname = 'config_test.json'
    bak_fname = 'copy_config.json'
    # Backup existing configuration file
    if os.path.isfile(fname):
        shutil.copy(fname, bak_fname)
    # Save custom configuration to JSON file
    with open(fname,'w') as fp:
        json.dump(custom_conf, fp, indent=2)

if __name__ == '__main__':
    generate_config()
