import sys
sys.path.append("./")
import base64
from datetime import datetime, timedelta
import os
#import signal
import subprocess
import atexit
from threading import Thread
from time import sleep
import screeninfo
import flet as ft
# import psutil
import requests

from src.common.cloud_settings import get_settings


# RELEASE = True
# FIXED_FPS = 150

finish_time: datetime|None = None
starting = False
stopping = False
process: subprocess.Popen|None = None
SERVER_URL = 'http://127.0.0.1:4242'

video_image = 'iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAApgAAAKYB3X3' \
              '/OAAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAANCSURBVEiJtZZPbBtFFMZ' \
               '/M7ubXdtdb1xSFyeilBapySVU8h8OoFaooFSqiihIVIpQBKci6KEg9Q6H9kovIHoCIVQJJCKE1ENFjnAgcaSGC6rEnxBwA04T' \
               'x43t2FnvDAfjkNibxgHxnWb2e/u992bee7tCa00YFsffekFY' \
               '+nUzFtjW0LrvjRXrCDIAaPLlW0nHL0SsZtVoaF98mLrx3pdhOqLtYPHChahZcYYO7KvPFxvRl5XPp1sN3adWiD1ZAqD6XYK1b' \
               '/dvE5IWryTt2udLFedwc1+9kLp+vbbpoDh+6TklxBeAi9TL0taeWpdmZzQDry0AcO+jQ12RyohqqoYoo8RDwJrU' \
               '+qXkjWtfi8Xxt58BdQuwQs9qC/afLwCw8tnQbqYAPsgxE1S6F3EAIXux2oQFKm0ihMsOF71dHYx' \
               '+f3NND68ghCu1YIoePPQN1pGRABkJ6Bus96CutRZMydTl+TvuiRW1m3n0eDl0vRPcEysqdXn+jsQPsrHMquGeXEaY4Yk4wxWcY5V' \
               '/9scqOMOVUFthatyTy8QyqwZ' \
               '+kDURKoMWxNKr2EeqVKcTNOajqKoBgOE28U4tdQl5p5bwCw7BWquaZSzAPlwjlithJtp3pTImSqQRrb2Z8PHGigD4RZuNX6JYj6wj' \
               '7O4TFLbCO/Mn/m8R+h6rYSUb3ekokRY6f/YukArN979jcW+V/S8g0eT/N3VN3kTqWbQ428m9/8k0P' \
               '/1aIhF36PccEl6EhOcAUCrXKZXXWS3XKd2vc/TRBG9O5ELC17MmWubD2nKhUKZa26Ba2+D3P+4/MNCFwg59oWVeYhkzgN' \
               '/JDR8deKBoD7Y+ljEjGZ0sosXVTvbc6RHirr2reNy1OXd6pJsQ+gqjk8VWFYmHrwBzW/n+uMPFiRwHB2I7ih8ciHFxIkd/3Omk5tCDV1t' \
               '+2nNu5sxxpDFNx+huNhVT3/zMDz8usXC3ddaHBj1GHj/As08fwTS7Kt1HBTmyN29vdwAw+/wbwLVOJ3uAD1wi' \
               '/dUH7Qei66PfyuRj4Ik9is+hglfbkbfR3cnZm7chlUWLdwmprtCohX4HUtlOcQjLYCu' \
               '+fzGJH2QRKvP3UNz8bWk1qMxjGTOMThZ3kvgLI5AzFfo379UAAAAASUVORK5CYII='



def get_image():
    return video_image


def get_rest_seconds():
    if finish_time:
        return (finish_time - datetime.now()).total_seconds()
    else:
        return None


def get_notify_style():
    if starting:
        return ft.colors.GREY, 'запуск сервера...'
    if stopping:
        return ft.colors.GREY, 'остановка сервера...'
    rest_seconds = get_rest_seconds()
    if rest_seconds and rest_seconds > 0:
        h, remainder = divmod(rest_seconds, 3600)
        m, s = divmod(remainder, 60)
        return (
            ft.colors.GREEN,
            f'для сохранения точности показаний перезапустите сервер через {int(h)}:{int(m)}:{int(s)}'
        )
    elif rest_seconds:
        return ft.colors.RED, 'требуется перезапуск сервера'
    else:
        return ft.colors.ORANGE, 'сервер не запущен'

def get_video_feed(page):
    state = 'off'
    while True:
        if finish_time is None:
            state = 'off'
            sleep(1)
            continue
        if state == 'off':
            sleep(5)
            state = 'on'
        try:
            response = requests.get(f'{SERVER_URL}/video_feed', stream=True, )
            content = b''
            delimiter = b'--frame\r\nContent-Type: image/jpeg\r\n\r\n'
            for data in response.iter_content(8192):
                content += data
                if delimiter in content:
                    elements = content.split(delimiter)
                    prev = elements[0].strip(b'\r\n')
                    if prev:
                        b64image = base64.b64encode(prev).decode('UTF-8')
                        page.controls[0].tabs[1].content.controls[0].src_base64 = b64image
                        page.update()
                    current = elements[1].strip(b'\r\n')
                    # sleep(0.3)
                    content = current
        except:
            sleep(5)

def refresh_page(page: ft.Page,
                 timer_label: ft.Text,
                 start_button: ft.ElevatedButton,
                 stop_button: ft.ElevatedButton,
                 calibrate_button: ft.ElevatedButton):
    while True:
        notify_color, notify_text = get_notify_style()
        timer_label.color = notify_color
        timer_label.value = notify_text
        start_button.disabled = starting or (finish_time is not None)
        stop_button.disabled = stopping or (finish_time is None)
        calibrate_button.disabled = stopping or (finish_time is None)
        page.update()
        sleep(0.3)


def main(page: ft.Page):
    page.title = 'PathFinder utility'
    page.window.min_width = 650
    page.window.width = 650
    notify_column, timer_label = get_notify_label()
    button_container, start_button, stop_button, calibrate_button = get_buttons(page)
    
    settings, token, msg = get_settings()
    # print(msg)
    frequencies = [100]
    if settings is not None and 'frequencies' in settings and len(settings['frequencies'])>0:
        frequencies = settings['frequencies']
    monitors = screeninfo.get_monitors() 
    if monitors:
        monitors = sorted(monitors, key=lambda x:x.name)
        monitors = [(m.name, f"{m.width_mm}x{m.height_mm}мм {', основной' if m.is_primary else ''}") for m in monitors]
    else:
        monitors = [('', 'Основной')]
    tabs = ft.Tabs(
        animation_duration=300,
        scrollable=False,
        tabs=[
            ft.Tab(
                text='Настройки',
                content=ft.Column(controls=[notify_column, *get_parameters(page, frequencies, monitors), button_container])
            ),
            ft.Tab(
                text='Камера',
                content=ft.Column([ft.Image(src_base64=get_image())])
            ),
        ]
    )
    page.add(tabs)
    Thread(target=get_video_feed, daemon=True, args=(page,)).start()
    Thread(target=refresh_page, daemon=True, args=(page, timer_label, start_button, stop_button, calibrate_button)).start()


def get_notify_label():
    header = ft.Text(value='Внимание! Предельное время работы сервера составляет 3 часа.',
                     theme_style=ft.TextThemeStyle.TITLE_LARGE)
    color, text = get_notify_style()
    footer = ft.Text(value=text, color=color)
    return ft.Column(controls=[header, footer], spacing=5, run_spacing=5), footer


def get_parameters(page: ft.Page, frequencies: list[float], monitors: list[str]):
    def get_option(value):
        return ft.dropdown.Option(value)

    def setting_changed(e: ft.ControlEvent):
        control: ft.Dropdown = e.control
        page.client_storage.set(control.key, control.value)

    protocol = page.client_storage.get('protocol') or 'HTTP'
    video = page.client_storage.get('video') or 'Камера'
    gpu_type = page.client_storage.get('gpu_type') or 'NVIDIA GPU'
    freq = page.client_storage.get('freq') or frequencies[0]
    focus = page.client_storage.get('focus') or '16'
    # print(page.client_storage.get('monitor'), monitors[0], page.client_storage.get('monitor') or monitors[0])
    monitor = page.client_storage.get('monitor') or monitors[0][0]
    res = [
        # ft.Dropdown(label="Протокол",
        #             autofocus=True,
        #             options=[get_option('HTTP'), get_option('TCP')],
        #             value=protocol,
        #             key='protocol',
        #             on_change=setting_changed),
        ft.Dropdown(label="Тип видеопотока",
                    options=[get_option('Камера'), get_option('Демо видео')],
                    value=video,
                    key='video',
                    on_change=setting_changed),
        # ft.Dropdown(label="Способ обработки",
        #             options=[get_option('NVIDIA GPU'), get_option('Intel GPU')],
        #             value=gpu_type,
        #             key='gpu_type',
        #             on_change=setting_changed),
        ft.Dropdown(label="Частота, Гц",
                    options=[get_option(x) for x in frequencies],
                    value=freq,
                    key='freq',
                    on_change=setting_changed),
        ft.Dropdown(label="Фокусное расстояние, мм",
                    options=[get_option('8'), get_option('12'), get_option('16')],
                    value=focus,
                    key='focus',
                    on_change=setting_changed),
        ft.Dropdown(label="Используемый экран",
                    options=[ft.dropdown.Option(key, text) for (key, text) in monitors],
                    value=monitor,
                    key='monitor',
                    on_change=setting_changed)
    ]
    # if FIXED_FPS is not None:
    #     res.pop(1)
    return res


# def kill_server():
#     if RELEASE:
#         return
#     for proc in psutil.process_iter():
#         for conns in proc.net_connections(kind='inet'):
#             if conns.laddr.port == 4242:
#                 try:
#                     proc.send_signal(signal.SIGTERM) # or SIGKILL
#                     sleep(5)
#                 except:
#                     pass


def get_buttons(page):
    def start(e: ft.ControlEvent):
        global finish_time
        global starting
        global process
        btn: ft.ElevatedButton = e.control
        btn.disabled = True
        starting = True
        current_working_directory = os.getcwd()
        selectors: list[ft.Dropdown] = page.controls[0].tabs[0].content.controls[1:]
        params = []
        # if selectors[0].value == 'TCP':
        #     params.append('--tcp-socket')
        if selectors[0].value == 'Демо видео':
            params.append('-v')
        # if selectors[2].value == 'NVIDIA GPU':
        #     params.append('--nvidia')
        params.append('--fps')
        # if FIXED_FPS is None:
        params.append(f'{selectors[1].value}')
        params.append('--focus')
        params.append(f'{selectors[2].value}')
        params.append('--monitor')
        params.append(f'{selectors[3].value}')
        # else:
        #     params.append(f'{FIXED_FPS}')
        #     params.append('--focus')
        #     params.append(f'{selectors[1].value}')
        print(sys.executable)
        # subprocess.Popen([sys.executable, "main.py", *params], shell=True, cwd=current_working_directory, env=os.environ.copy())
        # kill_server()
        command = 'eyetracker.exe' # if RELEASE else 'start_server.bat'
        process = subprocess.Popen(
            [command, *params],
            shell=False,
            cwd=current_working_directory,
            env=os.environ.copy(),
            creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
        )
        atexit.register(process.terminate)
        sleep(5)
        print(f'PID: {process.pid}')
        finish_time = datetime.now() + timedelta(hours=3)
        starting = False

    def stop(e: ft.ControlEvent):
        global finish_time
        global stopping
        btn: ft.ElevatedButton = e.control
        stopping = True
        btn.disabled = True
        process.terminate()
        # kill_server()
        finish_time = None
        stopping = False

    def calibrate(_):
        try:
            requests.post(f'{SERVER_URL}/commands?command=<SET ID="CALIBRATE_SHOW" STATE="1" />')
            requests.post(f'{SERVER_URL}/commands?command=<SET ID="CALIBRATE_START" STATE="1" />')
        except:
            pass

    start_button = ft.ElevatedButton("Запустить", on_click=start)
    stop_button = ft.ElevatedButton("Остановить", on_click=stop)
    calibrate_button = ft.ElevatedButton("Калибровка", on_click=calibrate)
    return ft.Row(
        spacing=5, alignment=ft.MainAxisAlignment.CENTER, controls=[start_button, stop_button, calibrate_button]
    ), start_button, stop_button, calibrate_button


ft.app(target=main)
