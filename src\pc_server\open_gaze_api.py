#src/pc_server/open_gaze_api.py
"""
Open Gaze API Module
====================

This module defines the `OpenGazeAPI` class, which facilitates communication between the eye-tracking
system and external applications or services using the OpenGaze API protocol. It handles the parsing
and formatting of API commands, manages calibration settings, and transmits gaze data in the required format.

Key Features:
- Parses incoming API commands and parameters.
- Manages calibration settings and commands.
- Formats and sends gaze data based on enabled settings.
- Supports socket-based communication for real-time data transmission.
- Records calibration results for debugging and validation purposes.

Dependencies:
- OpenCV for image processing (though not directly used here, likely for integration purposes).
- NumPy for numerical operations.
- socket, threading, and queue for network communication and concurrent data handling.
- Custom modules: `common.eye_data`, `config`, and `calibration_models`.

Classes:
- `OpenGazeAPI`: Handles the core API functionalities including command parsing, settings management,
  and data transmission.
- `BaseApiTransport`: An abstract base class for different transport mechanisms (e.g., socket).
- `SocketProcess`: Implements socket-based communication adhering to the OpenGaze API protocol.


"""


from src.common.eye_data import *


import socket
# import multiprocessing as mp
from threading import Thread
from queue import Queue
import logging
import re
import typing as T
import os
import numpy as np


# TODO reset counter and timestamp after calibration
def parse_tag(tag: str):
    """
    Parses a single tag string into a list of commands with their parameters.

    Args:
        tag (str): The tag string containing commands and parameters in the format <COMMAND PARAM="VALUE" />.

    Returns:
        list of tuples: Each tuple contains a command string and a dictionary of its parameters.
                        Returns None if the tag format is invalid.
    """
    start = tag.find('<')
    end = tag.find('>')
    if start == -1 or end == -1 or end < start:
        logging.error(f'Wrong tag {tag}')
        return None
    tag = tag[start+1:end].upper()
    commands = []
    comm_str = tag.split('/')
    for c in comm_str:
        if len(c) < 3:
            continue
        params_str = c.split(' ')
        command = params_str.pop(0)
        while len(command) == 0:
            command = params_str.pop(0)
        if not command.isalpha():
            logging.warning(f'Command "{command}" contains not only letters')
        params = {}
        for p in params_str:
            if len(p) == 0:
                continue
            res = re.match('^([A-Z_]+)=\"([^\"]+)\"$', p)
            gr = []
            if res is not None:
                gr = res.groups()
            if len(gr) != 2:
                logging.warning(f'Parameter "{p}" has wrong structure (not PARAM="VALUE")')
                continue
            params[gr[0]] = gr[1]
        commands.append((command, params))
    return commands


class OpenGazeAPI:
    """
    Handles communication with external applications using the OpenGaze API protocol.

    Responsibilities:
    - Manage API settings and commands.
    - Format and send gaze data based on enabled settings.
    - Receive and process incoming API commands.
    - Record calibration results for debugging purposes.

    Attributes:
        setting_commands (list): List of supported setting commands.
        calibrate_commands (list): List of supported calibration commands.
        calibr_record_command (list): List of calibration record commands.
        settings (dict): Dictionary mapping setting names to their `Setting` objects.
        counter (int): Counter for sent data records.
        use_socket (bool): Flag indicating whether to use socket communication.
    """
    setting_commands = ['ENABLE_SEND_COUNTER', 'ENABLE_SEND_TIME', 'ENABLE_SEND_POG_FIX', 'ENABLE_SEND_POG_BEST',
                        'ENABLE_SEND_POG_LEFT', 'ENABLE_SEND_POG_RIGHT', 'ENABLE_SEND_EYE_LEFT',
                        'ENABLE_SEND_EYE_RIGHT', 'ENABLE_SEND_PUPILMM', 'ENABLE_SEND_OPENMM',
                        'ENABLE_SEND_BLINK', 'ENABLE_SEND_DATA',
                        'CALIBRATE_SHOW', 'CALIBRATE_START', 'LAST_CALIBR_RESULT']
    calibrate_commands = ['CALIBRATE_SHOW', 'CALIBRATE_START', 'CALIBRATE_RESULT_SUMMARY', 'CALIBRATE_RESET',
                          'CALIBRATE_ADDPOINT']
    calibr_record_command = ['CALIB_RESULT']

    class Setting:
        """
         Represents a single API setting with its value, read-only status, and formatter function.

         Attributes:
             value (bool): The current value of the setting.
             readonly (bool): Indicates if the setting is read-only.
             formatter (Callable): Function to format the setting value for transmission.
         """

        def __init__(self, value: bool = False, readonly: bool = False,
                     formatter: T.Callable = lambda x: int(x)):
            # self.type = _type
            self.value = value
            self.readonly = readonly
            self.formatter = formatter

    def __init__(self, q_send: Queue, q_receive: Queue, q_records: Queue, pc_server, use_socket=False):
        """
        Initializes the OpenGazeAPI with communication queues and calibration server.

        Args:
            q_send (Queue): Queue to send data to external applications.
            q_receive (Queue): Queue to receive data from external applications.
            q_records (Queue): Queue to record sent data for debugging.
            pc_server: Reference to the PC server handling calibration.
            use_socket (bool, optional): If True, use socket communication. Defaults to False.
        """
        self.q_send = q_send
        self.q_receive = q_receive
        self.q_records = q_records
        self.pc_server = pc_server
        self.use_socket = use_socket
        self.settings: dict[str, OpenGazeAPI.Setting] = {}
        self.counter = 0
        for c in OpenGazeAPI.setting_commands:
            self.settings[c] = OpenGazeAPI.Setting()
        self.settings['LAST_CALIBR_RESULT'].readonly = True

    # def format_setting_record(self, rec:str):
    #     if rec

    def get_settings(self):
        """
        Retrieves the current settings as a dictionary.

        Returns:
            dict: Dictionary of setting names and their integer values.
        """
        return {k: int(v.value) for k, v in self.settings.items()}
    
    def send_frame_data(self, data: FrameData):
        """
        Formats and sends frame data based on enabled settings.

        Args:
            data (FrameData): The frame data containing eye gaze information.

        Returns:
            dict: Dictionary of formatted data sent.
        """

        def prepare_value(setting: str, keys, values, send_valid=False, valid_key=None, multiplier=None):
            """
            Prepares individual data fields based on the setting and formats them.

            Args:
                setting (str): The setting name to check if data should be sent.
                keys (str or tuple): The keys corresponding to the data fields.
                values (str or tuple): The values to be formatted and sent.
                send_valid (bool, optional): If True, sends a validity flag. Defaults to False.
                valid_key (str, optional): The key for the validity flag. Defaults to None.
                multiplier (float, optional): Multiplier to apply to the values. Defaults to None.
            """
            assert setting in self.settings
            if not self.settings[setting].value:
                return
            data_valid = True
            if type(keys) is str:
                keys = (keys,)
                values = (values,)
            assert len(keys) == len(values)
            for i, k in enumerate(keys):
                val = values[i]
                if val is None or np.isnan(val):
                    val = 0.
                    data_valid = False
                if multiplier is not None:
                    val *= multiplier
                if isinstance(values[i], float):
                    val = f'{val:.5f}'
                elif type(values[i]) is bool:
                    val = int(val)
                data_dict[k.upper()] = val
            if send_valid:
                if valid_key is None:
                    valid_key = keys[0] + 'V'
                data_dict[valid_key] = int(data_valid)
            # data_list.append(data_dict)

        data_dict = {}
        if not self.settings['ENABLE_SEND_DATA'].value:
            return data_dict
        
        prepare_value('ENABLE_SEND_COUNTER', 'CNT', self.counter)
        prepare_value('ENABLE_SEND_TIME', 'TIME', data.timestamp)
        bpog = [0.]*2
        avg = 0
        for k, d in data.eye_data.items():
            if d.pog[0] is not None:
                bpog[0] += d.pog[0]
                bpog[1] += d.pog[1]
                avg += 1
        if avg == 0:
            bpog = [None]*2
        if avg == 2:
            bpog[0] /= 2
            bpog[1] /= 2
        prepare_value('ENABLE_SEND_POG_LEFT', ['LPOGX', 'LPOGY'], data.eye_data['left'].pog, True, 'LPOGV')
        prepare_value('ENABLE_SEND_POG_RIGHT', ['RPOGX', 'RPOGY'], data.eye_data['right'].pog, True, 'RPOGV')
        prepare_value('ENABLE_SEND_POG_BEST', ['BPOGX', 'BPOGY'], bpog, True, 'BPOGV')
        prepare_value('ENABLE_SEND_EYE_LEFT', ['LEYEX', 'LEYEY', 'LEYEZ'],
                      data.eye_data['left'].gaze_origin, True, 'LPUPILV', 1E-3)
        prepare_value('ENABLE_SEND_EYE_RIGHT', ['REYEX', 'REYEY', 'REYEZ'],
                      data.eye_data['right'].gaze_origin, True, 'RPUPILV', 1E-3)
        prepare_value('ENABLE_SEND_EYE_LEFT', ['LGAZEX', 'LGAZEY', 'LGAZEZ'],
                      data.eye_data['left'].gaze_vec, True, 'LGAZEV')
        prepare_value('ENABLE_SEND_EYE_RIGHT', ['RGAZEX', 'RGAZEY', 'RGAZEZ'],
                      data.eye_data['right'].gaze_vec, True, 'RGAZEV')
        prepare_value('ENABLE_SEND_PUPILMM', 'LPMM', data.eye_data['left'].pupil_diam, True)
        prepare_value('ENABLE_SEND_PUPILMM', 'RPMM', data.eye_data['right'].pupil_diam, True)
        prepare_value('ENABLE_SEND_OPENMM', 'LOPENMM', data.eye_data['left'].opening, True)
        prepare_value('ENABLE_SEND_OPENMM', 'ROPENMM', data.eye_data['right'].opening, True)

        # 'ENABLE_SEND_POG_FIX'

        if len(data_dict) > 0:
            # Format data into OpenGaze API XML-like string
            data_str = '<REC '
            for k, v in data_dict.items():
                data_str += f'{k.upper()}="{str(v)}" '
            data_str += '/>'
            if self.use_socket:
                self.send(data_str)
            else:
                self.q_records.put((self.counter, data_str.encode('utf-8')))
            self.counter += 1
        return data_dict
    
    def send(self, data):
        """
        Sends data through the send queue.

        Args:
            data (str): The data string to send.
        """
        self.q_send.put(data.encode('utf-8'))

    def send_calib_result(self):
        """
        Sends calibration results to the external application.

        NOTE: Currently returns immediately without sending any data.
        TODO: Implement sending of calibration results correctly.
        """
        return
        # TODO: fix
        data_str = '<CAL ID="CALIB_RESULT" '
        for i, p in enumerate(self.pc_server.calibration_model.calibr_data):
            n = i+1
            x = p.pog[0]
            y = p.pog[1]
            data_str += f'CALX{n}="{x:.5f}" CALY{n}="{y:.5f}" LX{n}="{x:.5f}" LY{n}="{y:.5f}" LV{n}="1" '
        data_str += '/>'
        self.send(data_str)

    def receive(self):
        """
        Continuously receives and processes incoming API commands from the receive queue.

        Parses the commands and executes corresponding actions such as getting or setting settings.
        """
        while True:
            try:
                data = self.q_receive.get(timeout=1.)
            except:
                continue
            data = str(data)
            logging.debug(f'Data received:{data}')
            commands = parse_tag(data)
            if commands is None:
                continue
            if not len(commands):
                logging.warning(f"No commands found in {data}")
            for c in commands:
                com, params = c
                if com == 'GET':
                    if 'ID' in params:
                        if params['ID'] in self.settings:
                            setting = self.settings[params['ID']]
                            self.send(f'<ACK ID="{params["ID"]}" STATE="{setting.formatter(setting.value)}" />')
                        else:
                            logging.warning(f'Unknown command in GET: {params["ID"]}')
                    else:
                        logging.warning(f'No ID passed in GET: "{data}"')
                elif com == 'SET':
                    if 'ID' in params and 'STATE' in params:
                        if params['ID'] in self.settings:
                            setting = self.settings[params['ID']]
                            if setting.readonly:
                                logging.warning(f'Trying to SET readonly parameter {params["ID"]}')
                                self.send(f'<NACK ID="{params["ID"]}" STATE="{setting.formatter(setting.value)}" />')
                            else:
                                logging.info(f'Setting {params["ID"]} to {params["STATE"]}')
                                t = type(setting.value)
                                if t == bool:
                                    setting.value = bool(int(params['STATE']))
                                else:
                                    setting.value = type(setting.value)(params['STATE'])
                                if params['ID'] == 'CALIBRATE_START':
                                    self.pc_server.calibr_start = setting.value
                                elif params['ID'] == 'CALIBRATE_SHOW':
                                    self.pc_server.calibr_show = setting.value
                                self.send(f'<ACK ID="{params["ID"]}" STATE="{setting.formatter(setting.value)}" />')
                                # logging.info(f'Reply: <ACK ID="{params["ID"]}" STATE="{setting.formatter(setting.value)}" />')
                        elif params['ID'] == 'CALIBRATE_RESULT_SUMMARY':
                            self.send('<ACK ID="CALIBRATE_RESULT_SUMMARY" AVE_ERROR="0.00" VALID_POINTS="4" />')
                        else:
                            logging.warning(f'Unknown command in SET: {params["ID"]}')
                    else:
                        logging.warning(f'No ID or STATE passed in SET: "{data}"')
                else:
                    logging.warning(f'Unrecognized command: "{data}"')


class BaseApiTransport:
    """
    Abstract base class for API transport mechanisms.

    Attributes:
        q_send (Queue): Queue for sending data.
        q_recv (Queue): Queue for receiving data.
    """

    def __init__(self, q_send: Queue, q_recv: Queue):
        self.q_send = q_send
        self.q_recv = q_recv


# TODO: Implement socket reconnection logic
class SocketProcess(BaseApiTransport):
    """
    Implements socket-based communication adhering to the OpenGaze API protocol.

    Responsibilities:
    - Establish and manage socket connections with external clients.
    - Send and receive data through the socket.
    - Handle concurrent data transmission and reception using threads.
    """

    def __init__(self, q_send: Queue, q_recv: Queue):
        """
        Initializes the SocketProcess with binding and listening on a specific port.

        Args:
            q_send (Queue): Queue to send data to the client.
            q_recv (Queue): Queue to receive data from the client.
        """
        super().__init__(q_send, q_recv)
        self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        self.socket.bind(('', 4242))
        self.socket.listen(1)

    @staticmethod
    def recv_thread(sock: socket, q_recv: Queue):
        """
        Thread function to receive data from the socket and put it into the receive queue.

        Args:
            sock (socket): The socket connection to receive data from.
            q_recv (Queue): Queue to place received data.
        """
        while True:
            data = sock.recv(1024)
            if not data:
                break
            if len(data) > 0:
                logging.debug(f'Received "{data}"')
                q_recv.put(data)
        logging.info('Connection aborted')
        sock.close()

    def run(self):
        """
        Runs the socket process, handling incoming connections and data transmission.

        Continuously listens for new client connections and manages data sending and receiving.
        """
        logging.info(f'Socket proc started, PID:{os.getpid()}')
        while True:
            logging.info(f'Awaiting client...')
            conn, addr = self.socket.accept()
            # TODO: reset settings after disconnecting
            logging.info(f'Connection established from {addr}')
            receiver = Thread(target=self.recv_thread, args=(conn, self.q_recv))
            receiver.daemon = True
            receiver.start()
            try:
                while True:
                    # print(self.q_send.qsize())
                    if conn.fileno() == -1: # socket closed
                        break
                    while self.q_send.qsize() > 0:
                        data = self.q_send.get()
                        logging.debug(f'Sending "{data}"')
                        conn.send(data+b'\r\n')
            finally:
                logging.info('Closing connection')
                conn.close()
                # TODO stop receiver thread
