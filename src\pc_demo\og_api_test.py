import socket

class SocketProcess:
    def __init__(self, data):
        self.data_list = data
        self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)

    def run(self):
        self.socket.connect(('127.0.0.1', 4242))
        for d in self.data_list:
            self.socket.send(d+b'\r\n')
            data = self.socket.recv(1024)
            if len(data) > 0:
                print(data)
        while True:
            data = self.socket.recv(1024)
            if len(data) > 0:
                print(data)


if __name__ == '__main__':
    sock = SocketProcess([
        b'<SET ID="ENABLE_SEND_COUNTER" STATE="1"/>',
        b'<SET ID="ENABLE_SEND_TIME" STATE="1" />',
        b'<SET ID="ENABLE_SEND_EYE_LEFT" STATE="1" />',
        b'<SET ID="ENABLE_SEND_EYE_RIGHT" STATE="1" />',
        b'<SET ID="ENABLE_SEND_POG_LEFT" STATE="1" />',
        b'<SET ID="ENABLE_SEND_POG_BEST" STATE="1" />',
        b'<SET ID="ENABLE_SEND_POG_RIGHT" STATE="1" />',
        b'<SET ID="ENABLE_SEND_DATA" STATE="1" />',
        # b'<SET ID="CALIBRATE_START" STATE="1" />',
    ])
    sock.run()
