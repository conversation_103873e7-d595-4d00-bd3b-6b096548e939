#src/pc_server/calibration_procedure.py

import logging
import datetime
from threading import Thread
import time
import os
# import multiprocessing as mp
from queue import Queue
import numpy as np
import json
import cv2
from screeninfo import Monitor
import pygetwindow as gw
from _queue import Empty as QueueEmpty

from src.common.eye_data import *
from src.common.config import Conf
from .calibration_models import CalibrationModel
from ..common.json_encoder import JSONEncoder


class CalibrationProcedure:
    """
     Manages the calibration procedure for an eye-tracking system.

     Responsibilities:
     - Display calibration points to the user.
     - Collect and process gaze data from the user's eyes.
     - Validate calibration accuracy.
     - Provide visual feedback and handle user inputs during calibration.
     - Record calibration data for debugging purposes.

     Attributes:
         WIN_NAME (str): Name of the calibration window.
         DRAW_VIEW_DIVIDER (int): Factor to divide screen resolution for drawing.
         RECORD_DEBUG_CALIBR_DATA (bool): Flag to enable recording of debug calibration data.
         FRAME_OK_COLOR (tuple): BGR color tuple for indicating good frame data.
         FRAME_BAD_COLOR (tuple): BGR color tuple for indicating bad frame data.
         BACKGROUND_COLOR (tuple): BGR color tuple for the background.
         FRAME_THICKNESS (int): Thickness of frame outlines.
         FRAME_TIMEOUT (float): Timeout for frame retrieval in seconds.
         BAD_TIMEOUT (int): Threshold for determining bad calibration frames.
     """
    WIN_NAME = 'Calibration'
    DRAW_VIEW_DIVIDER = 2
    RECORD_DEBUG_CALIBR_DATA = False
    FRAME_OK_COLOR = (0, 255, 0)
    FRAME_BAD_COLOR = (0, 0, 255)
    BACKGROUND_COLOR = (0, 0, 0)
    FRAME_THICKNESS = 4
    FRAME_TIMEOUT = 0.2
    BAD_TIMEOUT = 100 # TODO fps changing issues
    def __init__(self, 
                 calibration_points: list[CalibrPoint], 
                 validation_points: list[CalibrPoint],
                 q_cal_frame_data: Queue,
                 q_cal_commands: Queue,
                 q_cal_data: Queue,
                 conf: Conf,
                 monitor: Monitor,
                 validate: bool = True,
                 ):
        """
           Initializes the CalibrationProcedure with necessary parameters and sets up the calibration window.

           Args:
               calibration_points (List[CalibrPoint]): List of calibration points for the user to look at.
               validation_points (List[CalibrPoint]): List of validation points to assess calibration accuracy.
               q_cal_frame_data (Queue): Queue to receive frame data for calibration.
               q_cal_commands (Queue): Queue to receive calibration commands.
               q_cal_data (Queue): Queue to send calibration results.
               conf (Conf): Configuration object containing system settings.
               validate (bool, optional): Flag to determine if validation should be performed after calibration. Defaults to True.
               monitor (Monitor, optional): Specific monitor to use for calibration display. If None, the primary monitor is used.
           """
        self.conf = conf
        self.skip = 3 if self.conf['fps'] > 250 else 0
        self.num_points_avg = int(self.conf['fps'] / (self.skip + 1) * self.conf['calibr_fixation_time'])
        if self.num_points_avg < self.conf['calibr_min_points']:
            self.num_points_avg = self.conf['calibr_min_points']
        self.q_cal_frame_data = q_cal_frame_data
        self.q_cal_commands = q_cal_commands
        self.q_cal_data = q_cal_data
        self.calibration_points = calibration_points
        self.validation_points = validation_points
        self.n_points = len(calibration_points)
        self.gaze_bad_cnt = 0
        self.skip_cnt = 0 
        self.start_ticks = 0
        self.start_point_time = 0

        self.monitor = monitor

        self.calibration_model = CalibrationModel(self.monitor, self.conf)

        self.validate = validate
        
        self.current_point_data = []
        self.recorded_data = []
        self.frame_data: list[list[FrameData]] = [[]]
        self.groups = []
        self.calibr_json = {'aborted': False, 'summary': [{}]*self.n_points, 'corners': [{}]*self.n_points}

        # Initialize calibration canvas
        self.canvas = np.zeros((self.monitor.height//self.DRAW_VIEW_DIVIDER, 
                                   self.monitor.width//self.DRAW_VIEW_DIVIDER, 3),
                                  dtype=np.uint8)
        cv2.namedWindow(self.WIN_NAME, cv2.WINDOW_NORMAL)
        cv2.moveWindow(self.WIN_NAME, self.monitor.x, self.monitor.y)
        cv2.setWindowProperty(self.WIN_NAME, cv2.WND_PROP_FULLSCREEN, cv2.WINDOW_FULLSCREEN)
        
        self.draw_gaze = False # Flag to enable drawing gaze vectors
        self.draw_frame = False  # Flag to enable drawing frame outlines
        # self.validate = True
        # self.load_

    @staticmethod
    def activate_window_by_title():
        """
        Activates the calibration window by minimizing and maximizing it to bring it to the foreground.
        Retries up to 100 times in case of failure.
        """
        windows = gw.getWindowsWithTitle(CalibrationProcedure.WIN_NAME)
        for window in windows:
            if window.title != CalibrationProcedure.WIN_NAME:
                # print(f'Title: {window.title}')
                continue
            for _ in range(100):
                try:
                    window.minimize()
                    window.maximize()
                    window.activate()
                    break
                except Exception as error:
                    time.sleep(0.1)
                    print(error)

    def numpy_gaze(self, f: FrameData):
        """
              Converts gaze data from a FrameData object into a NumPy array for easier processing.

              Args:
                  f (FrameData): The frame data containing eye gaze information.

              Returns:
                  np.ndarray: A NumPy array with shape (2, 2, 3) representing (eye, origin/gaze_vec, xyz).
              """
        gaze = np.zeros((2, 2, 3), dtype=np.float32)  # (eye, origin/gaze_vec, xyz)
        for i, eye in enumerate(f.eye_data):
            if f.eye_data[eye].gaze_origin[0] is not None:
                gaze[i, 0, :] = f.eye_data[eye].gaze_origin
                gaze[i, 1, :] = f.eye_data[eye].gaze_vec
        return gaze

    def eye_data_gaze(self, gaze: np.array, p=None):
        """
             Populates a CalibrPoint object with origin and gaze vector data from a NumPy array.

             Args:
                 gaze (np.array): A NumPy array containing origin and gaze vectors.
                 p (CalibrPoint, optional): An existing CalibrPoint object to populate. Creates a new one if None.

             Returns:
                 CalibrPoint: The populated calibration point with gaze data.
        """
        if p is None:
            p = CalibrPoint()
        for i, eye in enumerate(['left', 'right']):
            p.eye_data[eye].gaze_origin = gaze[i, 0, :]
            p.eye_data[eye].gaze_vec = gaze[i, 1, :]
        return p


    def group_points(self, threshold=0.001):
        """
        Groups calibration points based on a similarity threshold to identify clusters of consistent data.

        Args:
            threshold (float, optional): Distance threshold for grouping points. Defaults to 0.001.
        """
        def get_distance(p1, p2):
            """
             Calculates the distance between two points based on the dot product of their gaze vectors.

             Args:
                 p1 (np.ndarray): First point's gaze data.
                 p2 (np.ndarray): Second point's gaze data.

             Returns:
                 float: Calculated distance metric.
             """
            gaze_vec1 = (p1[0, 1] + p1[1, 1]) / 2
            gaze_vec2 = (p2[0, 1] + p2[1, 1]) / 2
            # return np.abs(np.cross(gaze_vec1, gaze_vec2)) 
            # return 1 - np.dot(p1[0, 1], p2[0, 1])
            return np.linalg.norm(gaze_vec1 - gaze_vec2)

        points = self.current_point_data.copy()
        self.groups = []
        while points:
            far_points = []
            ref = points.pop()
            self.groups.append([ref])
            for point in points:
                d = get_distance(ref, point)
                if d < self.conf['calibr_sin_thr']:
                    self.groups[-1].append(point)
                else:
                    far_points.append(point)
            points = far_points
        # perform average operation on each group
        # return [list(np.mean(g, axis=1).astype(int)) for g in groups]

    def current_point_calibrated(self):
        """
        Determines if the current calibration point has been adequately calibrated based on collected data.

        Returns:
            bool: True if calibration is successful for the current point, False otherwise.
        """
        if len(self.current_point_data) < self.num_points_avg:
            return False
        self.group_points()
        q_group = [len(g) for g in self.groups]
        max_group = np.argmax(q_group)
        logging.debug(f'{q_group=}')
        if q_group[max_group] >= self.num_points_avg:
            return True
        return False

    def get_averaged_gaze(self, p: CalibrPoint):
        """
        Calculates the average gaze data from the largest group of consistent points and updates the calibration point.

        Args:
            p (CalibrPoint): The calibration point to update with averaged gaze data.

        Returns:
            CalibrPoint: The updated calibration point with averaged gaze data.
        """
        q_group = [len(g) for g in self.groups]
        max_group = np.argmax(q_group)
        avg_gaze = np.mean(self.groups[max_group], axis=0)
        return self.eye_data_gaze(avg_gaze, p)

    def record_calibr_json(self):
        """
        Records the calibration data into a JSON file for debugging purposes if enabled.
        """
        if not self.RECORD_DEBUG_CALIBR_DATA:
            return
        os.makedirs('./calibr_data', exist_ok=True)
        fname = datetime.datetime.now().strftime("%Y_%m_%d__%H_%M_%S")
        fname = f'./calibr_data/{fname}.json'
        j = json.dumps(self.calibr_json, indent=2)
        with open(fname, 'w') as f:
            f.write(j)

    def draw_text(self, text:str, center=(0.5, 0.5), font=cv2.FONT_HERSHEY_COMPLEX, scale=1, 
                  color=(255, 255, 255), thickness=1, interval = 0.5, relative=True):
        """
        Draws multi-line text on the calibration canvas at the specified center position.

        Args:
            text (str): The text to display. Use '\n' for multiple lines.
            center (tuple, optional): Center position as a fraction (x, y) of the canvas size. Defaults to (0.5, 0.5).
            font (int, optional): OpenCV font type. Defaults to cv2.FONT_HERSHEY_COMPLEX.
            scale (float, optional): Font scale factor. Defaults to 1.
            color (tuple, optional): Text color in BGR format. Defaults to white.
            thickness (int, optional): Thickness of the text strokes. Defaults to 1.
            interval (float, optional): Vertical spacing between lines as a multiplier of text height. Defaults to 0.5.
            relative (bool, optional): If True, `center` is treated as a fraction of canvas size. Otherwise, absolute pixels. Defaults to True.
        """
        h, w = self.canvas.shape[0:2]
        if relative:
            center = (int(center[0]*w), int(center[1]*h))
        text_lines = text.split('\n')
        n = len(text_lines)
        for i, line in enumerate(text_lines):
            (text_width, text_height), _ = cv2.getTextSize(line, font, scale, thickness)
            text_x = center[0] - text_width // 2
            text_y = center[1] + text_height // 2 + int((i + 0.5 - n/2) * (1+interval) * text_height)
            cv2.putText(self.canvas, line, (text_x, text_y), font, scale, color, thickness, cv2.LINE_AA)
    
    def draw_marker(self, p: CalibrPoint|tuple[float,float], marker_type=cv2.MARKER_CROSS, size=20, color=(0, 0, 255), relative=True):
        """
        Draws a marker (e.g., cross) on the calibration canvas at the specified point.

        Args:
            p (CalibrPoint or tuple): The point to draw the marker at. If CalibrPoint, uses the POG attribute.
            marker_type (int, optional): OpenCV marker type. Defaults to cv2.MARKER_CROSS.
            size (int, optional): Size of the marker. Defaults to 20.
            color (tuple, optional): Marker color in BGR format. Defaults to red.
            relative (bool, optional): If True, coordinates are treated as fractions of canvas size. Otherwise, absolute pixels. Defaults to True.
        """
        h, w = self.canvas.shape[0:2]
        if type(p) == CalibrPoint:
            xc = p.pog[0]
            yc = p.pog[1]
        else:
            xc = p[0]
            yc = p[1]
        if relative:
            xc = int(xc*w)
            yc = int(yc*h)
        cv2.drawMarker(self.canvas, (xc, yc), color, markerType=marker_type, markerSize=size, thickness=1)

    # draws circle if point is on screen or arrow if outside
    def draw_gaze_point(self, x, y, color=(255, 0, 0), r=30, relative=True):
        """
        Draws a circle if the gaze point is on the screen or an arrow if it's outside.

        Args:
            x (float): X-coordinate of the gaze point.
            y (float): Y-coordinate of the gaze point.
            color (tuple, optional): Color of the gaze point in BGR format. Defaults to blue.
            r (int, optional): Radius of the circle or size parameter for the arrow. Defaults to 30.
            relative (bool, optional): If True, coordinates are treated as fractions of canvas size. Otherwise, absolute pixels. Defaults to True.
        """
        h, w = self.canvas.shape[0:2]
        if relative:
            x = int(x*w)
            y = int(y*h)
        if -r < x < w + r and -r < y < h + r:
            # blue = canvas[:, :, 0]
            cv2.circle(self.canvas, (x, y), r, color, -1)
        else:
            d = 2*r//3
            dx = 0
            dy = 0
            if x < 0:
                x = 0
                dx = -d
            if x >= w:
                x = w - 1
                dx = d
            if y < 0:
                y = 0
                dy = -d
            if y >= h:
                y = h - 1
                dy = d
            if dx != 0 and dy != 0:
                dx = int(dx * 0.7)
                dy = int(dy * 0.7)
            pts = [np.array([(x, y), (x - dx - dy, y + dx - dy), (x - dx + dy, y - dx - dy)])]
            cv2.drawContours(self.canvas, pts, 0, color, -1)

    def check_gaze_ok(self, data: FrameData):
        """
        Checks if the gaze data from both eyes is valid and consistent.

        Args:
            data (FrameData): The frame data containing eye gaze information.

        Returns:
            bool: True if gaze data is valid and consistent, False otherwise.
        """
        if data.eye_data['left'].gaze_vec[0] is None or \
           data.eye_data['right'].gaze_vec[0] is None or \
           np.isnan(data.eye_data['left'].gaze_vec[0]) or \
           np.isnan(data.eye_data['right'].gaze_vec[0]):
            logging.debug('At least one eye not detected')
            return False
        # Check if the gaze vectors of both eyes are nearly aligned
        if np.dot(data.eye_data['left'].gaze_vec, data.eye_data['right'].gaze_vec) < self.conf['filter_bad_gaze_thr']:
            logging.debug(f'cos < {self.conf["filter_bad_gaze_thr"]}')
            return False
        return True
    
    def gaze_ok(self, data: FrameData|None = None):
        """
        Tracks the number of consecutive bad gaze frames to determine overall gaze quality.

        Args:
            data (FrameData, optional): The frame data to evaluate. If None, only checks the current bad count.

        Returns:
            bool: True if the number of bad gaze frames is below the threshold, False otherwise.
        """
        if data is not None:
            if self.check_gaze_ok(data):
                self.gaze_bad_cnt = 0
            else:
                self.gaze_bad_cnt += 1
        return self.gaze_bad_cnt < self.BAD_TIMEOUT

    def iterate_run(self, p:CalibrPoint, waiting: bool):
        """
        Iterates through calibration frames, handling user input and updating the calibration canvas.

        Args:
            p (CalibrPoint): The current calibration point.
            waiting (bool): Flag indicating whether the procedure is waiting to start calibrating the point.

        Returns:
            str or None: Returns 'abort' if calibration is aborted, 'start' to begin calibration, or None otherwise.
        """
        redraw = False
        key = cv2.pollKey()
        if key == 27:  # Abort calibration with ESC key
            self.calibr_json['aborted'] = True
            self.record_calibr_json()
            return 'abort'
        elif waiting and key == 32:  # Start calibration point with SPACE key
            self.start_ticks = cv2.getTickCount()
            self.start_point_time = time.time()
            return 'start'
        # elif key == ord('v'):
        #     self.draw_frame = not self.draw_frame
        try:
            data: FrameData = self.q_cal_frame_data.get(timeout=self.FRAME_TIMEOUT)
        except QueueEmpty:
            time.sleep(self.FRAME_TIMEOUT)
            return
        self.skip_cnt += 1
        if self.skip_cnt > self.skip:
            self.skip_cnt = 0
        else:
            return
        # Determine frame color based on gaze quality
        color = self.FRAME_OK_COLOR if self.gaze_ok(data) else self.FRAME_BAD_COLOR
        # Choose marker type based on whether waiting to start calibration
        marker_type = cv2.MARKER_CROSS if waiting else cv2.MARKER_TILTED_CROSS
        self.draw_marker(p, marker_type, color=color)
        redraw = True
        if waiting and self.draw_gaze:
            # Fade the canvas slightly for visual effect
            self.canvas = (self.canvas * 0.9).astype(np.uint8)
            self.draw_marker(p)
            for eye_ind, eye in enumerate(['left', 'right']):
                pog = data.eye_data[eye].pog
                if pog[0] is not None:
                    self.draw_gaze_point(pog[0], pog[1], (0, 180, 240 * eye_ind))
            redraw = True
        if self.draw_frame:
            # Draw a rectangle around the canvas to indicate frame boundaries
            cv2.rectangle(self.canvas,
                        (0, 0),
                        (self.canvas.shape[1] - 1, self.canvas.shape[0] - 1),
                        color=color,
                        thickness=self.FRAME_THICKNESS*2 - 1)
            redraw = True
        if not waiting and self.check_gaze_ok(data):
            assert self.start_ticks != 0
            # TODO: fix for separate device
            if data.ticks < self.start_ticks:
                return
            if time.time() - self.start_point_time > 0.5:
                self.current_point_data.append(self.numpy_gaze(data))
                self.frame_data[-1].append(data)
            if len(self.current_point_data) > self.conf['calibr_duration_ratio'] * self.num_points_avg:
                self.current_point_data.pop(0)
                self.frame_data[-1].pop(0)
        # redraw = True
        if redraw:
            cv2.imshow(self.WIN_NAME, self.canvas)
        return
           
    def validation(self):
        """
        Validates the calibration by comparing calculated POGs against validation points.

        Returns:
            str: 'ok' if calibration is successful, 'repeat' to redo calibration, or 'abort' to cancel.
        """
        bad_points = 0
        self.canvas = np.zeros_like(self.canvas)
        for p in self.validation_points:
            self.draw_marker(p, cv2.MARKER_TILTED_CROSS)
        
        # Store correction vectors for each calibration point
        correction_errors = []
        
        for i in range(len(self.frame_data)):
            val_p = np.array(self.validation_points[i].pog)
            deviations = []
            point_pogs = []
            for p in self.frame_data[i]:
                self.calibration_model.calc_pog(p)
                pog = np.zeros((2,2), dtype=float)
                pog[0] = np.array(p.eye_data['left'].pog)
                pog[1] = np.array(p.eye_data['right'].pog)
                if not np.isnan(np.min(pog)):
                    avg_pog = np.mean(pog, axis=0)
                    deviations.append(avg_pog - val_p)
                    point_pogs.append(avg_pog)
                    self.draw_marker(tuple(avg_pog.tolist()), cv2.MARKER_TILTED_CROSS, size=3, color=(0, 255, 0))
            
            if point_pogs:
                acc_sq = np.sqrt(np.sum(np.square(np.mean(deviations, axis=0))))
                prec_sq = np.sqrt(np.sum(np.square(np.std(deviations, axis=0))))
                color = self.FRAME_OK_COLOR
                if acc_sq > 0.1 or prec_sq > 0.1:
                    bad_points += 1
                    color = self.FRAME_BAD_COLOR
                text_x = val_p[0]*0.8+0.1
                text_y = val_p[1] + 0.1 if val_p[1] < 0.6 else val_p[1] - 0.1
                self.draw_text(f"acc={acc_sq:.3f}\nprec={prec_sq:.3f}", (text_x, text_y), color=color, scale=0.5)
                # self.draw_text(f"prec={prec_sq:.3f}", (val_p[0]*0.7+0.15, val_p[1]*0.7+0.20), color=color, scale=0.5)
            
                
                # Calculate mean POG for this validation point
                mean_pog = np.mean(point_pogs, axis=0)
                # Calculate correction vector (error)
                error_vector = val_p - mean_pog
                correction_errors.append((val_p, error_vector))
                
                # Visualize error
                # cv2.line(self.canvas, 
                #         (int(mean_pog[0]*self.canvas.shape[1]), int(mean_pog[1]*self.canvas.shape[0])),
                #         (int(val_p[0]*self.canvas.shape[1]), int(val_p[1]*self.canvas.shape[0])),
                #         (0,0,255), 1)
                
                if np.linalg.norm(error_vector) > self.conf['calibr_validation_threshold']:
                    bad_points += 1

        # Store correction vectors in calibration model
        self.calibration_model.correction_errors = correction_errors
        # self.calibration_model.correction_enabled = True

        fail = bad_points > 2
        text = "Калибровка пройдена успешно.\nДля начала эксперимента нажмите кнопку ENTER" if not fail \
        else "Рекомендуем пройти калибровку еще раз.\nДля повторной калибровки нажмите кнопку ПРОБЕЛ\nДля начала эксперимента с текущей калибровкой нажмите ENTER"
        self.draw_text(text, (0.5, 0.35), scale=0.6)
        cv2.imshow(self.WIN_NAME, self.canvas)
        while True:
            key = cv2.pollKey()
            if fail and key == 32: # SPACE key to repeat calibration
                return 'repeat'
            elif key == 27:  # ESC key to abort
                return 'abort'
            elif key == 13:  # ENTER key to confirm calibration
                return 'ok'
            time.sleep(0.1)
        
    def do_run(self, validation = False):
        """
        Executes the calibration or validation procedure by iterating through calibration points.

        Args:
            validation (bool, optional): If True, performs validation after calibration. Defaults to False.

        Returns:
            Union[List[CalibrPoint], str, None]: Returns calibrated points, 'ok', 'repeat', or 'abort'.
        """
        self.frame_data = []
        points = self.validation_points if validation else self.calibration_points           
        for i, p in enumerate(points):
            self.frame_data.append([])
            self.canvas = np.zeros_like(self.canvas)
            # color = self.FRAME_OK_COLOR if self.gaze_ok() else self.FRAME_BAD_COLOR
            # self.draw_marker(p, color = color)
            # cv2.imshow(self.WIN_NAME, self.canvas)
            #waiting for start calibrating point
            while True:
                res = self.iterate_run(p, True)
                if res  == 'abort':
                    return
                elif res == 'start':
                    break
            logging.info(f'start calibrating point {i}')
            self.canvas = np.zeros_like(self.canvas)
            # self.draw_marker(p, marker_type=cv2.MARKER_TILTED_CROSS)
            # cv2.imshow(self.WIN_NAME, self.canvas)
            self.current_point_data = []
            while True:
                res = self.iterate_run(p, False)
                if res  == 'abort':
                    return
                if self.current_point_calibrated():
                    time.sleep(0.5)
                    if not validation:
                        if self.conf['send_deep_diagnostics'] != 'never':
                            self.recorded_data.append({'point': p, 'data': self.current_point_data.copy()})
                        self.calibration_points[i] = self.get_averaged_gaze(self.calibration_points[i])
                        cp = self.calibration_points[i].eye_data
                        self.calibr_json['summary'][i] = {'left': f'{cp["left"].gaze_vec[0]:.3f}, {cp["left"].gaze_vec[1]:.3f}', 
                                                      'right': f'{cp["right"].gaze_vec[0]:.3f}, {cp["right"].gaze_vec[1]:.3f}'}
                    break
            logging.info(f'point {i} calibrated successfully')
            
        if validation:
            res = self.validation()
            if res == 'abort':
                return
            else:
                return res
                
        else:
            self.record_calibr_json()
            return self.calibration_points

    def run(self):
        """
        Initiates and manages the entire calibration and validation process.

        Displays the calibration window, handles user interactions, and records calibration data.
        """
        cv2.imshow(self.WIN_NAME, self.canvas)
        activator = Thread(target=CalibrationProcedure.activate_window_by_title, daemon=True)
        activator.start()
        repeat = True
        res = None
        while repeat:
            repeat = False
            self.recorded_data = []
            res = self.do_run()
            if self.conf['debug']:
                print(f"{len(self.recorded_data)=}")
            if len(self.recorded_data) > 0:
                t = datetime.datetime.now().strftime('%Y_%m_%d__%H_%M_%S')
                with open(f'{self.conf["session_data_dir"]}deep_diag/calibr_points+{t}.json', 'w') as fp:
                    json.dump(self.recorded_data, fp, indent=2, cls=JSONEncoder)
            if res is not None and self.validate:
                self.calibration_model.calibr_data = res
                res2 = self.do_run(validation=True)
                if res2 is None:
                    res = None
                elif res2 == 'repeat':
                    repeat = True
        self.q_cal_data.put(res)
        cv2.destroyWindow(self.WIN_NAME)
        if activator.is_alive():
            activator.join()
        # TODO: Ensure all threads and resources are properly cleaned up
