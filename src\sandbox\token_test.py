import requests


CLOUD_HOSTS = {'http': "http://web.neuroiconica.com:8080", 'https': "https://web.neuroiconica.com:8080"}
CLOUD_PARAMS_URI = "/api/pathfinder_cloud/parameters/"
TOKEN = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJwYXRoZmluZGVyX2lkIjoyLCJvcmdhbml6YXRpb25faWQiOjEsImV4cCI6MTc0MjY0MDM0OCwiaWF0IjoxNzExMTA0MzQ4fQ.ZiB0ipqbyycHQi74bAcMUDSFl4NUytXmdCD_MqyO22Q"

for host in CLOUD_HOSTS:
    try:
        response = requests.get(CLOUD_HOSTS[host] + CLOUD_PARAMS_URI,
                                headers={'Authorization': f'Bearer {TOKEN}'})
        code = response.status_code
    except Exception as e:
        print (e)
        continue
    print(f"{host}: {code}")
