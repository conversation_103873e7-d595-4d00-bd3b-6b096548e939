import numpy as np
import onnxruntime as ort
import os
import sys
from src.common.config import Conf


# TODO setting fp precision
class InferenceOnnx:
    def __init__(self, model_file, input_shape, output_shape, conf:Conf):
        self.input_shape = input_shape
        self.output_shape = output_shape
        self.batch = self.input_shape[0]
        self.fp16 = 'fp16' in model_file
        try:
            base_path = sys._MEIPASS
        except:
            base_path = ''
        model_file = os.path.join(base_path, f'weights/{conf["weights_version"]}/onnx/b{self.batch}/{model_file}.onnx')
        # if self.fp16:
        #     model_file = model_file.replace('fp32', 'fp16')
        # if not os.path.exists(model_file):
        #     base_model_file = model_file.replace(f'/b{self.batch}', '')
        #     os.system(f'python -m onnxruntime.tools.make_dynamic_shape_fixed ' + \
        #               f'--dim_param batch_size --dim_value {self.batch} {base_model_file} {model_file}')
        # providers = ['TensorrtExecutionProvider', 'CUDAExecutionProvider'] 
        providers = ['CPUExecutionProvider']
        if conf['enable_onnx_gpu']:
            providers = ['CUDAExecutionProvider', 'CPUExecutionProvider']
        if conf['enable_onnx_trt']:
            providers = ['TensorrtExecutionProvider', 'CUDAExecutionProvider'] 
        print(providers)
        options: ort.SessionOptions = ort.SessionOptions()
        # if self.output_shape[1]  == 3:
        options.log_severity_level = 3
        self.model = ort.InferenceSession(model_file, sess_options=options, providers=providers)

    def inference(self, img):
        # if self.batch == 1:
        #     assert self.input_shape[2:4] == img.shape
        # else:
        #     assert self.input_shape == img.shape
        inf_type = np.float16 if self.fp16 else np.float32
        img = (img * (1/255.)).astype(inf_type)
        if self.batch==1 and len(img.shape) != 4:
            img = np.expand_dims(img, axis=0)
            img = np.stack([img]*self.batch)
        assert len(img.shape)==4
        # print(111)
        outputs = self.model.run(None, {'input': img})
        # print(222)

        out = outputs[0]
        if self.fp16:
            out = out.astype(np.float32)
        return out
    
    def release(self):
        pass

