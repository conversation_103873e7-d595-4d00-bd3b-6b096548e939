import cv2
from src.eyetracker.video_sources.BaseVideoSource import BaseVideoSource
from src.common.config import FRAME_HEIGHT, FRAME_WIDTH, Conf
import logging
import time


class VideoFile(BaseVideoSource):
    def __init__(self, conf:Conf, cycle=True):
        super().__init__(conf)
        self.cycle = cycle
        self.filename = conf['video_file_name']
        self.buffer = []
        self.frame_index = 0
        self.sleep_time = conf['video_file_sleep']
        self.cap = cv2.VideoCapture(self.filename)
        cnt = 0
        while True:
            cnt += 1
            if cnt > 1000:
                break
            success, frame = self.cap.read()
            if success:
                frame = cv2.resize(frame, (FRAME_WIDTH, FRAME_HEIGHT))
                self.buffer.append(frame[:,:,0])
            else:
                break
        self.cap.release()
        self.end = False
        self.camera_info['type'] = 'Video file'

    def get_frame(self):
        if self.sleep_time > 0:
            time.sleep(self.sleep_time)
        frame = self.buffer[self.frame_index]
        self.frame_index += 1
        if self.frame_index >= len(self.buffer):
            if self.cycle:
                self.frame_index = 0
            else:
                self.end = True
        # success, self.frame = self.cap.read()
        # self.end = not success
        # if self.end and self.cycle:
        #     self.cap = cv2.VideoCapture(self.filename)
        #     success, self.frame = self.cap.read()
        #     self.end = not success
        # frame = cv2.resize(frame, (1440, 1080))
        # time.sleep(1E-2)
        return frame, None

    def end(self):
        return self.end

    def release(self):
        self.cap.release()


'''
cap = cv2.VideoCapture(fname)

fname = 'p01_60cm_550mA_60fps_0.avi'
split = os.path.split(fname)
split = list(split)
split[-1] = 'out_' + split[-1]
out_fname = './'.join(split)

_, frame = cap.read()
if frame is None:
    break
frame = frame[:, :, 0]
'''
