# Unified Calibration Procedure - Implementation Summary

## Overview

The eye tracking calibration procedure has been completely rewritten to meet the following requirements:

1. **Use pygame instead of OpenCV for GUI** ✅
2. **Store averaged eye_center and glints values** ✅  
3. **Single-pass calibration with validation on same data** ✅
4. **Time-based stimulus with shrinking circle** ✅

## Key Changes

### 1. New Pygame-Based GUI (`pygame_calibration_gui.py`)

- **Replaced OpenCV window management** with pygame for better cross-platform compatibility
- **Fullscreen display support** with proper monitor positioning
- **Shrinking stimulus visualization** - circle that reduces in radius over time
- **Frame border feedback** for gaze quality indication
- **Event handling** for user input (SPACE to start, ESC to abort)

Key features:
```python
def draw_shrinking_stimulus(self, x, y, progress, max_radius=30):
    # Draws circle that shrinks from max_radius to min_radius based on progress (0.0 to 1.0)
```

### 2. Unified Calibration Procedure (`unified_calibration_procedure.py`)

**Major architectural changes:**

#### Time-Based Data Collection
- **Removed grouping logic** - no more clustering of similar gaze points
- **Fixed 2-second stimulus duration** per calibration point
- **Data trimming** - excludes first and last 0.5 seconds of data
- **Continuous data collection** during valid time window

#### New Data Structure
```python
data_point = {
    'gaze_data': numpy_gaze_array,
    'frame_data': FrameData_object, 
    'timestamp': elapsed_time_since_start
}
```

#### Stimulus Presentation
- **Shrinking circle stimulus** that reduces in size over 2 seconds
- **Visual feedback** showing remaining time and point progress
- **Immediate data collection** without separate calibration/validation phases

### 3. Enhanced Data Storage

#### Extended CalibrPoint Structure
- **Stores averaged eye_center and glints** in addition to gaze_origin and gaze_vec
- **All eye tracking data preserved** during averaging process

#### Averaging Algorithm
```python
def average_eye_data(self, frame_data_list):
    # Averages all collected data:
    # - eye_center coordinates
    # - glints positions (both glints)
    # - gaze_origin (3D position)
    # - gaze_vec (normalized direction)
```

### 4. Integration with Existing System

#### Configuration Option
- **`use_unified_calibration`** flag in config (default: True)
- **Backward compatibility** - old calibration procedure still available
- **Seamless integration** with pc_server.py

#### Updated Dependencies
- **Added pygame 2.6.1** to requirements.txt
- **Updated imports** to use screeninfo.Monitor directly

## Usage

### Running the New Calibration

The new calibration procedure is enabled by default. To use it:

1. **Automatic**: Set `use_unified_calibration: true` in config (default)
2. **Manual**: Call `UnifiedCalibrationProcedure` directly

### Calibration Flow

1. **Point Display**: Shows calibration point with instruction
2. **User Input**: Press SPACE to start stimulus
3. **Shrinking Stimulus**: 2-second circle animation
4. **Data Collection**: Collects data from 0.5s to 1.5s (middle 1 second)
5. **Averaging**: Calculates mean of all collected data
6. **Validation**: Performs validation on same collected data
7. **Repeat**: For all calibration points

### Key Benefits

1. **Simplified Process**: Single pass through calibration points
2. **Consistent Timing**: Fixed 2-second stimulus duration
3. **Better Data Quality**: Excludes initial fixation and final movement periods
4. **Enhanced Storage**: Preserves all eye tracking parameters
5. **Modern GUI**: Pygame-based interface with better visual feedback

## Files Modified

### New Files
- `src/pc_server/pygame_calibration_gui.py` - Pygame GUI implementation
- `src/pc_server/unified_calibration_procedure.py` - Main calibration logic
- `src/pc_server/test_unified_calibration.py` - Test suite
- `src/pc_server/demo_unified_calibration.py` - Demo application

### Modified Files
- `requirements.txt` - Added pygame dependency
- `src/common/eye_data.py` - Updated CalibrPoint documentation
- `src/common/config.py` - Added use_unified_calibration flag
- `src/pc_server/pc_server.py` - Integration with existing system

## Testing

Run the test suite:
```bash
cd src/pc_server
python test_unified_calibration.py
```

Run the demo:
```bash
cd src/pc_server  
python demo_unified_calibration.py
```

## Configuration

Key configuration parameters:
```python
{
    'use_unified_calibration': True,  # Enable new procedure
    'calibr_validation_threshold': 0.05,  # Validation accuracy threshold
    'fps': 60,  # Frame rate for timing calculations
}
```

The new system maintains full compatibility with existing calibration models and validation logic while providing a more streamlined and consistent user experience.
