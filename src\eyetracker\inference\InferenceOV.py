import numpy as np
import openvino.runtime as ov
import config 

core = ov.Core()


class InferenceOV:
    def __init__(self, model_file, input_shape, output_shape):
        self.input_shape = input_shape
        self.output_shape = output_shape
        self.batch = self.input_shape[0]
        model_file = f'weights/{config.WEIGHTS_VERSION}/openvino/b{self.batch}/{model_file}.xml'
        self.model = core.compile_model(model_file, 'CPU')
        self.request = self.model.create_infer_request()

    def inference(self, img):
        assert self.input_shape[2:4] == img.shape
        img = (img * (1/255.)).astype(np.float32)
        img = np.expand_dims(img, axis=0)
        img = np.stack([img]*self.batch)
        self.request.set_input_tensor(ov.Tensor(img))
        self.request.start_async()
        self.request.wait()
        outputs = self.request.get_output_tensor().data
        return outputs[0]
    
    def release(self):
        pass

