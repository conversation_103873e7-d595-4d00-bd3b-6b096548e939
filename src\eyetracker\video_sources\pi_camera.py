import subprocess as sp
import atexit

'''
# "raspividyuv" is the command that provides camera frames in YUV format
#  "--output -" specifies stdout as the output
#  "--timeout 0" specifies continuous video
#  "--luma" discards chroma channels, only luminance is sent through the pipeline
# see "raspividyuv --help" for more information on the parameters
# videoCmd = "raspividyuv -w "+str(w)+" -h "+str(h)+" --output - --timeout 0 --framerate "+str(fps)+" --luma --nopreview"
videoCmd = "libcamera-vid --codec yuv420 --width " + str(FRAME_WIDTH) + " --height " + str(
    FRAME_HEIGHT) + " --output - --timeout 0 --framerate " + str(FPS) + " --nopreview"
# videoCmd = "libcamera-vid --codec yuv420 --output - --timeout 0 --framerate "+str(fps)+" --nopreview"
videoCmd = videoCmd.split()  # Popen requires that each parameter is a separate string

if camera:
    cameraProcess = sp.Popen(videoCmd, stdout=sp.PIPE)  # start the camera
    atexit.register(
        cameraProcess.terminate)  # this closes the camera process in case the python scripts exits unexpectedly

    # wait for the first frame and discard it (only done to measure time more accurately)
    # rawStream = cameraProcess.stdout.read(bytesPerFrame)

    print("Recording...")


###

if camera:
    cameraProcess.stdout.flush()  # discard any frames that we were not able to process in time
    # Parse the raw stream into a numpy array
    frame = np.frombuffer(cameraProcess.stdout.read(bytesPerFrame), dtype=np.uint8)
    # t1 = time.perf_counter_ns()
    cameraProcess.stdout.read(bytesPerFrame // 2)
    # t2 = time.perf_counter_ns()
    if frame.size != bytesPerFrame:
        print("Error: Camera stream closed unexpectedly")
        print(f"{frame.size} {bytesPerFrame}")
        break
    frame = frame[0:FRAME_HEIGHT * FRAME_WIDTH]
    frame.shape = (FRAME_HEIGHT, FRAME_WIDTH)  # set the correct dimensions for the numpy array
'''
