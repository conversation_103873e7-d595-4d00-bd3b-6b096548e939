<?xml version="1.0"?>
<mapping>
	<map>
		<framework name="input" output_port_id="input" />
		<IR name="input" output_port_id="0" />
	</map>
	<map>
		<framework name="onnx::Conv_341" output_port_id="onnx::Conv_341" />
		<IR name="onnx::Conv_341" output_port_id="0" />
	</map>
	<map>
		<framework name="/inc/double_conv/double_conv.0/Conv" output_port_id="/inc/double_conv/double_conv.0/Conv_output_0" />
		<IR name="/inc/double_conv/double_conv.0/Conv" output_port_id="2" />
	</map>
	<map>
		<framework name="/inc/double_conv/double_conv.2/Relu" output_port_id="/inc/double_conv/double_conv.2/Relu_output_0" />
		<IR name="/inc/double_conv/double_conv.2/Relu" output_port_id="1" />
	</map>
	<map>
		<framework name="onnx::Conv_344" output_port_id="onnx::Conv_344" />
		<IR name="onnx::Conv_344" output_port_id="0" />
	</map>
	<map>
		<framework name="/inc/double_conv/double_conv.3/Conv" output_port_id="/inc/double_conv/double_conv.3/Conv_output_0" />
		<IR name="/inc/double_conv/double_conv.3/Conv" output_port_id="2" />
	</map>
	<map>
		<framework name="/inc/double_conv/double_conv.5/Relu" output_port_id="/inc/double_conv/double_conv.5/Relu_output_0" />
		<IR name="/inc/double_conv/double_conv.5/Relu" output_port_id="1" />
	</map>
	<map>
		<framework name="/down1/maxpool_conv/maxpool_conv.0/MaxPool" output_port_id="/down1/maxpool_conv/maxpool_conv.0/MaxPool_output_0" />
		<IR name="/down1/maxpool_conv/maxpool_conv.0/MaxPool" output_port_id="1" />
	</map>
	<map>
		<framework name="onnx::Conv_347" output_port_id="onnx::Conv_347" />
		<IR name="onnx::Conv_347" output_port_id="0" />
	</map>
	<map>
		<framework name="/down1/maxpool_conv/maxpool_conv.1/double_conv/double_conv.0/Conv" output_port_id="/down1/maxpool_conv/maxpool_conv.1/double_conv/double_conv.0/Conv_output_0" />
		<IR name="/down1/maxpool_conv/maxpool_conv.1/double_conv/double_conv.0/Conv" output_port_id="2" />
	</map>
	<map>
		<framework name="/down1/maxpool_conv/maxpool_conv.1/double_conv/double_conv.2/Relu" output_port_id="/down1/maxpool_conv/maxpool_conv.1/double_conv/double_conv.2/Relu_output_0" />
		<IR name="/down1/maxpool_conv/maxpool_conv.1/double_conv/double_conv.2/Relu" output_port_id="1" />
	</map>
	<map>
		<framework name="onnx::Conv_350" output_port_id="onnx::Conv_350" />
		<IR name="onnx::Conv_350" output_port_id="0" />
	</map>
	<map>
		<framework name="/down1/maxpool_conv/maxpool_conv.1/double_conv/double_conv.3/Conv" output_port_id="/down1/maxpool_conv/maxpool_conv.1/double_conv/double_conv.3/Conv_output_0" />
		<IR name="/down1/maxpool_conv/maxpool_conv.1/double_conv/double_conv.3/Conv" output_port_id="2" />
	</map>
	<map>
		<framework name="/down1/maxpool_conv/maxpool_conv.1/double_conv/double_conv.5/Relu" output_port_id="/down1/maxpool_conv/maxpool_conv.1/double_conv/double_conv.5/Relu_output_0" />
		<IR name="/down1/maxpool_conv/maxpool_conv.1/double_conv/double_conv.5/Relu" output_port_id="1" />
	</map>
	<map>
		<framework name="/down2/maxpool_conv/maxpool_conv.0/MaxPool" output_port_id="/down2/maxpool_conv/maxpool_conv.0/MaxPool_output_0" />
		<IR name="/down2/maxpool_conv/maxpool_conv.0/MaxPool" output_port_id="1" />
	</map>
	<map>
		<framework name="onnx::Conv_353" output_port_id="onnx::Conv_353" />
		<IR name="onnx::Conv_353" output_port_id="0" />
	</map>
	<map>
		<framework name="/down2/maxpool_conv/maxpool_conv.1/double_conv/double_conv.0/Conv" output_port_id="/down2/maxpool_conv/maxpool_conv.1/double_conv/double_conv.0/Conv_output_0" />
		<IR name="/down2/maxpool_conv/maxpool_conv.1/double_conv/double_conv.0/Conv" output_port_id="2" />
	</map>
	<map>
		<framework name="/down2/maxpool_conv/maxpool_conv.1/double_conv/double_conv.2/Relu" output_port_id="/down2/maxpool_conv/maxpool_conv.1/double_conv/double_conv.2/Relu_output_0" />
		<IR name="/down2/maxpool_conv/maxpool_conv.1/double_conv/double_conv.2/Relu" output_port_id="1" />
	</map>
	<map>
		<framework name="onnx::Conv_356" output_port_id="onnx::Conv_356" />
		<IR name="onnx::Conv_356" output_port_id="0" />
	</map>
	<map>
		<framework name="/down2/maxpool_conv/maxpool_conv.1/double_conv/double_conv.3/Conv" output_port_id="/down2/maxpool_conv/maxpool_conv.1/double_conv/double_conv.3/Conv_output_0" />
		<IR name="/down2/maxpool_conv/maxpool_conv.1/double_conv/double_conv.3/Conv" output_port_id="2" />
	</map>
	<map>
		<framework name="/down2/maxpool_conv/maxpool_conv.1/double_conv/double_conv.5/Relu" output_port_id="/down2/maxpool_conv/maxpool_conv.1/double_conv/double_conv.5/Relu_output_0" />
		<IR name="/down2/maxpool_conv/maxpool_conv.1/double_conv/double_conv.5/Relu" output_port_id="1" />
	</map>
	<map>
		<framework name="/Flatten" output_port_id="/Flatten_output_0" />
		<IR name="/Flatten" output_port_id="2" />
	</map>
	<map>
		<framework name="fc1.weight" output_port_id="fc1.weight" />
		<IR name="fc1.weight" output_port_id="0" />
	</map>
	<map>
		<framework name="/fc1/Gemm" output_port_id="/fc1/Gemm_output_0" />
		<IR name="/fc1/Gemm" output_port_id="2" />
	</map>
	<map>
		<framework name="/Relu" output_port_id="/Relu_output_0" />
		<IR name="/Relu" output_port_id="1" />
	</map>
	<map>
		<framework name="fc2.weight" output_port_id="fc2.weight" />
		<IR name="fc2.weight" output_port_id="0" />
	</map>
	<map>
		<framework name="/fc2/Gemm" output_port_id="/fc2/Gemm_output_0" />
		<IR name="/fc2/Gemm" output_port_id="2" />
	</map>
	<map>
		<framework name="/Relu_1" output_port_id="/Relu_1_output_0" />
		<IR name="/Relu_1" output_port_id="1" />
	</map>
	<map>
		<framework name="/Constant_3" output_port_id="/Constant_3_output_0" />
		<IR name="/Constant_3" output_port_id="0" />
	</map>
	<map>
		<framework name="/down3/maxpool_conv/maxpool_conv.0/MaxPool" output_port_id="/down3/maxpool_conv/maxpool_conv.0/MaxPool_output_0" />
		<IR name="/down3/maxpool_conv/maxpool_conv.0/MaxPool" output_port_id="1" />
	</map>
	<map>
		<framework name="onnx::Conv_359" output_port_id="onnx::Conv_359" />
		<IR name="onnx::Conv_359" output_port_id="0" />
	</map>
	<map>
		<framework name="/down3/maxpool_conv/maxpool_conv.1/double_conv/double_conv.0/Conv" output_port_id="/down3/maxpool_conv/maxpool_conv.1/double_conv/double_conv.0/Conv_output_0" />
		<IR name="/down3/maxpool_conv/maxpool_conv.1/double_conv/double_conv.0/Conv" output_port_id="2" />
	</map>
	<map>
		<framework name="/down3/maxpool_conv/maxpool_conv.1/double_conv/double_conv.2/Relu" output_port_id="/down3/maxpool_conv/maxpool_conv.1/double_conv/double_conv.2/Relu_output_0" />
		<IR name="/down3/maxpool_conv/maxpool_conv.1/double_conv/double_conv.2/Relu" output_port_id="1" />
	</map>
	<map>
		<framework name="onnx::Conv_362" output_port_id="onnx::Conv_362" />
		<IR name="onnx::Conv_362" output_port_id="0" />
	</map>
	<map>
		<framework name="/down3/maxpool_conv/maxpool_conv.1/double_conv/double_conv.3/Conv" output_port_id="/down3/maxpool_conv/maxpool_conv.1/double_conv/double_conv.3/Conv_output_0" />
		<IR name="/down3/maxpool_conv/maxpool_conv.1/double_conv/double_conv.3/Conv" output_port_id="2" />
	</map>
	<map>
		<framework name="/down3/maxpool_conv/maxpool_conv.1/double_conv/double_conv.5/Relu" output_port_id="/down3/maxpool_conv/maxpool_conv.1/double_conv/double_conv.5/Relu_output_0" />
		<IR name="/down3/maxpool_conv/maxpool_conv.1/double_conv/double_conv.5/Relu" output_port_id="1" />
	</map>
	<map>
		<framework name="/Shape" output_port_id="/Shape_2_output_0" />
		<IR name="/Shape" output_port_id="1" />
	</map>
	<map>
		<framework name="/Shape" output_port_id="/Shape_output_0" />
		<IR name="/Shape" output_port_id="1" />
	</map>
	<map>
		<framework name="/Shape" output_port_id="/Shape_1_output_0" />
		<IR name="/Shape" output_port_id="1" />
	</map>
	<map>
		<framework name="/Concat" output_port_id="/Concat_output_0" />
		<IR name="/Concat" output_port_id="2" />
	</map>
	<map>
		<framework name="/Reshape" output_port_id="/Reshape_output_0" />
		<IR name="/Reshape" output_port_id="2" />
	</map>
	<map>
		<framework name="up1.up.weight" output_port_id="up1.up.weight" />
		<IR name="up1.up.weight" output_port_id="0" />
	</map>
	<map>
		<framework name="/up1/up/ConvTranspose" output_port_id="/up1/up/ConvTranspose_output_0" />
		<IR name="/up1/up/ConvTranspose" output_port_id="2" />
	</map>
	<map>
		<framework name="/up1/Shape_2" output_port_id="/up1/Shape_output_0" />
		<IR name="/up1/Shape_2" output_port_id="1" />
	</map>
	<map>
		<framework name="/up1/Shape_2" output_port_id="/up1/Shape_2_output_0" />
		<IR name="/up1/Shape_2" output_port_id="1" />
	</map>
	<map>
		<framework name="/up1/Constant_2" output_port_id="/up1/Constant_2_output_0" />
		<IR name="/up1/Constant_2" output_port_id="0" />
	</map>
	<map>
		<framework name="/up1/Gather_2" output_port_id="/up1/Gather_2_output_0" />
		<IR name="/up1/Gather_2" output_port_id="3" />
	</map>
	<map>
		<framework name="/up1/Shape_3" output_port_id="/up1/Shape_1_output_0" />
		<IR name="/up1/Shape_3" output_port_id="1" />
	</map>
	<map>
		<framework name="/up1/Shape_3" output_port_id="/up1/Shape_3_output_0" />
		<IR name="/up1/Shape_3" output_port_id="1" />
	</map>
	<map>
		<framework name="/up1/Constant_3" output_port_id="/up1/Constant_3_output_0" />
		<IR name="/up1/Constant_3" output_port_id="0" />
	</map>
	<map>
		<framework name="/up1/Gather_3" output_port_id="/up1/Gather_3_output_0" />
		<IR name="/up1/Gather_3" output_port_id="3" />
	</map>
	<map>
		<framework name="/up1/Sub_1" output_port_id="/up1/Sub_1_output_0" />
		<IR name="/up1/Sub_1" output_port_id="2" />
	</map>
	<map>
		<framework name="/up1/Constant_4" output_port_id="/up1/Constant_4_output_0" />
		<IR name="/up1/Constant_4" output_port_id="0" />
	</map>
	<map>
		<framework name="/up1/Div" output_port_id="/up1/Cast_1_output_0" />
		<IR name="/up1/Div" output_port_id="2" />
	</map>
	<map>
		<framework name="/up1/Div" output_port_id="/up1/Div_output_0" />
		<IR name="/up1/Div" output_port_id="2" />
	</map>
	<map>
		<framework name="/up1/Div" output_port_id="/up1/Cast_output_0" />
		<IR name="/up1/Div" output_port_id="2" />
	</map>
	<map>
		<framework name="/up1/Unsqueeze_4" output_port_id="/up1/Unsqueeze_4_output_0" />
		<IR name="/up1/Unsqueeze_4" output_port_id="2" />
	</map>
	<map>
		<framework name="/up1/Sub_2" output_port_id="/up1/Sub_2_output_0" />
		<IR name="/up1/Sub_2" output_port_id="2" />
	</map>
	<map>
		<framework name="/up1/Unsqueeze_5" output_port_id="/up1/Unsqueeze_5_output_0" />
		<IR name="/up1/Unsqueeze_5" output_port_id="2" />
	</map>
	<map>
		<framework name="/up1/Constant" output_port_id="/up1/Constant_output_0" />
		<IR name="/up1/Constant" output_port_id="0" />
	</map>
	<map>
		<framework name="/up1/Gather" output_port_id="/up1/Gather_output_0" />
		<IR name="/up1/Gather" output_port_id="3" />
	</map>
	<map>
		<framework name="/up1/Constant_1" output_port_id="/up1/Constant_1_output_0" />
		<IR name="/up1/Constant_1" output_port_id="0" />
	</map>
	<map>
		<framework name="/up1/Gather_1" output_port_id="/up1/Gather_1_output_0" />
		<IR name="/up1/Gather_1" output_port_id="3" />
	</map>
	<map>
		<framework name="/up1/Sub" output_port_id="/up1/Sub_output_0" />
		<IR name="/up1/Sub" output_port_id="2" />
	</map>
	<map>
		<framework name="/up1/Constant_5" output_port_id="/up1/Constant_5_output_0" />
		<IR name="/up1/Constant_5" output_port_id="0" />
	</map>
	<map>
		<framework name="/up1/Div_1" output_port_id="/up1/Cast_3_output_0" />
		<IR name="/up1/Div_1" output_port_id="2" />
	</map>
	<map>
		<framework name="/up1/Div_1" output_port_id="/up1/Div_1_output_0" />
		<IR name="/up1/Div_1" output_port_id="2" />
	</map>
	<map>
		<framework name="/up1/Div_1" output_port_id="/up1/Cast_2_output_0" />
		<IR name="/up1/Div_1" output_port_id="2" />
	</map>
	<map>
		<framework name="/up1/Unsqueeze_6" output_port_id="/up1/Unsqueeze_6_output_0" />
		<IR name="/up1/Unsqueeze_6" output_port_id="2" />
	</map>
	<map>
		<framework name="/up1/Sub_3" output_port_id="/up1/Sub_3_output_0" />
		<IR name="/up1/Sub_3" output_port_id="2" />
	</map>
	<map>
		<framework name="/up1/Unsqueeze_7" output_port_id="/up1/Unsqueeze_7_output_0" />
		<IR name="/up1/Unsqueeze_7" output_port_id="2" />
	</map>
	<map>
		<framework name="/up1/Concat_1" output_port_id="/up1/Cast_4_output_0" />
		<IR name="/up1/Concat_1" output_port_id="4" />
	</map>
	<map>
		<framework name="/up1/Concat_1" output_port_id="/up1/Concat_1_output_0" />
		<IR name="/up1/Concat_1" output_port_id="4" />
	</map>
	<map>
		<framework name="/up1/ConstantOfShape" output_port_id="/up1/ConstantOfShape_output_0" />
		<IR name="/up1/ConstantOfShape" output_port_id="0" />
	</map>
	<map>
		<framework name="/up1/Concat_2" output_port_id="/up1/Concat_2_output_0" />
		<IR name="/up1/Concat_2" output_port_id="2" />
	</map>
	<map>
		<framework name="/up1/Constant_8" output_port_id="/up1/Constant_8_output_0" />
		<IR name="/up1/Constant_8" output_port_id="0" />
	</map>
	<map>
		<framework name="/up1/Reshape" output_port_id="/up1/Reshape_output_0" />
		<IR name="/up1/Reshape" output_port_id="2" />
	</map>
	<map>
		<framework name="/up1/Constant_10" output_port_id="/up1/Constant_10_output_0" />
		<IR name="/up1/Constant_10" output_port_id="0" />
	</map>
	<map>
		<framework name="/up1/Constant_11" output_port_id="/up1/Constant_11_output_0" />
		<IR name="/up1/Constant_11" output_port_id="0" />
	</map>
	<map>
		<framework name="/up1/Constant_12" output_port_id="/up1/Constant_12_output_0" />
		<IR name="/up1/Constant_12" output_port_id="0" />
	</map>
	<map>
		<framework name="/up1/Slice" output_port_id="/up1/Slice_output_0" />
		<IR name="/up1/Slice" output_port_id="4" />
	</map>
	<map>
		<framework name="/up1/Transpose" output_port_id="/up1/Transpose_output_0" />
		<IR name="/up1/Transpose" output_port_id="2" />
	</map>
	<map>
		<framework name="/up1/Reshape_1" output_port_id="/up1/Cast_5_output_0" />
		<IR name="/up1/Reshape_1" output_port_id="2" />
	</map>
	<map>
		<framework name="/up1/Reshape_1" output_port_id="/up1/Reshape_1_output_0" />
		<IR name="/up1/Reshape_1" output_port_id="2" />
	</map>
	<map>
		<framework name="/up1/Pad" output_port_id="/up1/Pad_output_0" />
		<IR name="/up1/Pad" output_port_id="4" />
	</map>
	<map>
		<framework name="/up1/Concat_3" output_port_id="/up1/Concat_3_output_0" />
		<IR name="/up1/Concat_3" output_port_id="2" />
	</map>
	<map>
		<framework name="onnx::Conv_365" output_port_id="onnx::Conv_365" />
		<IR name="onnx::Conv_365" output_port_id="0" />
	</map>
	<map>
		<framework name="/up1/conv/double_conv/double_conv.0/Conv" output_port_id="/up1/conv/double_conv/double_conv.0/Conv_output_0" />
		<IR name="/up1/conv/double_conv/double_conv.0/Conv" output_port_id="2" />
	</map>
	<map>
		<framework name="/up1/conv/double_conv/double_conv.2/Relu" output_port_id="/up1/conv/double_conv/double_conv.2/Relu_output_0" />
		<IR name="/up1/conv/double_conv/double_conv.2/Relu" output_port_id="1" />
	</map>
	<map>
		<framework name="onnx::Conv_368" output_port_id="onnx::Conv_368" />
		<IR name="onnx::Conv_368" output_port_id="0" />
	</map>
	<map>
		<framework name="/up1/conv/double_conv/double_conv.3/Conv" output_port_id="/up1/conv/double_conv/double_conv.3/Conv_output_0" />
		<IR name="/up1/conv/double_conv/double_conv.3/Conv" output_port_id="2" />
	</map>
	<map>
		<framework name="/up1/conv/double_conv/double_conv.5/Relu" output_port_id="/up1/conv/double_conv/double_conv.5/Relu_output_0" />
		<IR name="/up1/conv/double_conv/double_conv.5/Relu" output_port_id="1" />
	</map>
	<map>
		<framework name="up2.up.weight" output_port_id="up2.up.weight" />
		<IR name="up2.up.weight" output_port_id="0" />
	</map>
	<map>
		<framework name="/up2/up/ConvTranspose" output_port_id="/up2/up/ConvTranspose_output_0" />
		<IR name="/up2/up/ConvTranspose" output_port_id="2" />
	</map>
	<map>
		<framework name="/up2/Shape_2" output_port_id="/up2/Shape_output_0" />
		<IR name="/up2/Shape_2" output_port_id="1" />
	</map>
	<map>
		<framework name="/up2/Shape_2" output_port_id="/up2/Shape_2_output_0" />
		<IR name="/up2/Shape_2" output_port_id="1" />
	</map>
	<map>
		<framework name="/up2/Constant_2" output_port_id="/up2/Constant_2_output_0" />
		<IR name="/up2/Constant_2" output_port_id="0" />
	</map>
	<map>
		<framework name="/up2/Gather_2" output_port_id="/up2/Gather_2_output_0" />
		<IR name="/up2/Gather_2" output_port_id="3" />
	</map>
	<map>
		<framework name="/up2/Shape_3" output_port_id="/up2/Shape_1_output_0" />
		<IR name="/up2/Shape_3" output_port_id="1" />
	</map>
	<map>
		<framework name="/up2/Shape_3" output_port_id="/up2/Shape_3_output_0" />
		<IR name="/up2/Shape_3" output_port_id="1" />
	</map>
	<map>
		<framework name="/up2/Constant_3" output_port_id="/up2/Constant_3_output_0" />
		<IR name="/up2/Constant_3" output_port_id="0" />
	</map>
	<map>
		<framework name="/up2/Gather_3" output_port_id="/up2/Gather_3_output_0" />
		<IR name="/up2/Gather_3" output_port_id="3" />
	</map>
	<map>
		<framework name="/up2/Sub_1" output_port_id="/up2/Sub_1_output_0" />
		<IR name="/up2/Sub_1" output_port_id="2" />
	</map>
	<map>
		<framework name="/up2/Constant_4" output_port_id="/up2/Constant_4_output_0" />
		<IR name="/up2/Constant_4" output_port_id="0" />
	</map>
	<map>
		<framework name="/up2/Div" output_port_id="/up2/Div_output_0" />
		<IR name="/up2/Div" output_port_id="2" />
	</map>
	<map>
		<framework name="/up2/Div" output_port_id="/up2/Cast_1_output_0" />
		<IR name="/up2/Div" output_port_id="2" />
	</map>
	<map>
		<framework name="/up2/Div" output_port_id="/up2/Cast_output_0" />
		<IR name="/up2/Div" output_port_id="2" />
	</map>
	<map>
		<framework name="/up2/Unsqueeze_4" output_port_id="/up2/Unsqueeze_4_output_0" />
		<IR name="/up2/Unsqueeze_4" output_port_id="2" />
	</map>
	<map>
		<framework name="/up2/Sub_2" output_port_id="/up2/Sub_2_output_0" />
		<IR name="/up2/Sub_2" output_port_id="2" />
	</map>
	<map>
		<framework name="/up2/Unsqueeze_5" output_port_id="/up2/Unsqueeze_5_output_0" />
		<IR name="/up2/Unsqueeze_5" output_port_id="2" />
	</map>
	<map>
		<framework name="/up2/Constant" output_port_id="/up2/Constant_output_0" />
		<IR name="/up2/Constant" output_port_id="0" />
	</map>
	<map>
		<framework name="/up2/Gather" output_port_id="/up2/Gather_output_0" />
		<IR name="/up2/Gather" output_port_id="3" />
	</map>
	<map>
		<framework name="/up2/Constant_1" output_port_id="/up2/Constant_1_output_0" />
		<IR name="/up2/Constant_1" output_port_id="0" />
	</map>
	<map>
		<framework name="/up2/Gather_1" output_port_id="/up2/Gather_1_output_0" />
		<IR name="/up2/Gather_1" output_port_id="3" />
	</map>
	<map>
		<framework name="/up2/Sub" output_port_id="/up2/Sub_output_0" />
		<IR name="/up2/Sub" output_port_id="2" />
	</map>
	<map>
		<framework name="/up2/Constant_5" output_port_id="/up2/Constant_5_output_0" />
		<IR name="/up2/Constant_5" output_port_id="0" />
	</map>
	<map>
		<framework name="/up2/Div_1" output_port_id="/up2/Cast_3_output_0" />
		<IR name="/up2/Div_1" output_port_id="2" />
	</map>
	<map>
		<framework name="/up2/Div_1" output_port_id="/up2/Div_1_output_0" />
		<IR name="/up2/Div_1" output_port_id="2" />
	</map>
	<map>
		<framework name="/up2/Div_1" output_port_id="/up2/Cast_2_output_0" />
		<IR name="/up2/Div_1" output_port_id="2" />
	</map>
	<map>
		<framework name="/up2/Unsqueeze_6" output_port_id="/up2/Unsqueeze_6_output_0" />
		<IR name="/up2/Unsqueeze_6" output_port_id="2" />
	</map>
	<map>
		<framework name="/up2/Sub_3" output_port_id="/up2/Sub_3_output_0" />
		<IR name="/up2/Sub_3" output_port_id="2" />
	</map>
	<map>
		<framework name="/up2/Unsqueeze_7" output_port_id="/up2/Unsqueeze_7_output_0" />
		<IR name="/up2/Unsqueeze_7" output_port_id="2" />
	</map>
	<map>
		<framework name="/up2/Concat_1" output_port_id="/up2/Cast_4_output_0" />
		<IR name="/up2/Concat_1" output_port_id="4" />
	</map>
	<map>
		<framework name="/up2/Concat_1" output_port_id="/up2/Concat_1_output_0" />
		<IR name="/up2/Concat_1" output_port_id="4" />
	</map>
	<map>
		<framework name="/up2/ConstantOfShape" output_port_id="/up2/ConstantOfShape_output_0" />
		<IR name="/up2/ConstantOfShape" output_port_id="0" />
	</map>
	<map>
		<framework name="/up2/Concat_2" output_port_id="/up2/Concat_2_output_0" />
		<IR name="/up2/Concat_2" output_port_id="2" />
	</map>
	<map>
		<framework name="/up2/Constant_8" output_port_id="/up2/Constant_8_output_0" />
		<IR name="/up2/Constant_8" output_port_id="0" />
	</map>
	<map>
		<framework name="/up2/Reshape" output_port_id="/up2/Reshape_output_0" />
		<IR name="/up2/Reshape" output_port_id="2" />
	</map>
	<map>
		<framework name="/up2/Constant_10" output_port_id="/up2/Constant_10_output_0" />
		<IR name="/up2/Constant_10" output_port_id="0" />
	</map>
	<map>
		<framework name="/up2/Constant_11" output_port_id="/up2/Constant_11_output_0" />
		<IR name="/up2/Constant_11" output_port_id="0" />
	</map>
	<map>
		<framework name="/up2/Constant_12" output_port_id="/up2/Constant_12_output_0" />
		<IR name="/up2/Constant_12" output_port_id="0" />
	</map>
	<map>
		<framework name="/up2/Slice" output_port_id="/up2/Slice_output_0" />
		<IR name="/up2/Slice" output_port_id="4" />
	</map>
	<map>
		<framework name="/up2/Transpose" output_port_id="/up2/Transpose_output_0" />
		<IR name="/up2/Transpose" output_port_id="2" />
	</map>
	<map>
		<framework name="/up2/Reshape_1" output_port_id="/up2/Cast_5_output_0" />
		<IR name="/up2/Reshape_1" output_port_id="2" />
	</map>
	<map>
		<framework name="/up2/Reshape_1" output_port_id="/up2/Reshape_1_output_0" />
		<IR name="/up2/Reshape_1" output_port_id="2" />
	</map>
	<map>
		<framework name="/up2/Pad" output_port_id="/up2/Pad_output_0" />
		<IR name="/up2/Pad" output_port_id="4" />
	</map>
	<map>
		<framework name="/up2/Concat_3" output_port_id="/up2/Concat_3_output_0" />
		<IR name="/up2/Concat_3" output_port_id="2" />
	</map>
	<map>
		<framework name="onnx::Conv_371" output_port_id="onnx::Conv_371" />
		<IR name="onnx::Conv_371" output_port_id="0" />
	</map>
	<map>
		<framework name="/up2/conv/double_conv/double_conv.0/Conv" output_port_id="/up2/conv/double_conv/double_conv.0/Conv_output_0" />
		<IR name="/up2/conv/double_conv/double_conv.0/Conv" output_port_id="2" />
	</map>
	<map>
		<framework name="/up2/conv/double_conv/double_conv.2/Relu" output_port_id="/up2/conv/double_conv/double_conv.2/Relu_output_0" />
		<IR name="/up2/conv/double_conv/double_conv.2/Relu" output_port_id="1" />
	</map>
	<map>
		<framework name="onnx::Conv_374" output_port_id="onnx::Conv_374" />
		<IR name="onnx::Conv_374" output_port_id="0" />
	</map>
	<map>
		<framework name="/up2/conv/double_conv/double_conv.3/Conv" output_port_id="/up2/conv/double_conv/double_conv.3/Conv_output_0" />
		<IR name="/up2/conv/double_conv/double_conv.3/Conv" output_port_id="2" />
	</map>
	<map>
		<framework name="/up2/conv/double_conv/double_conv.5/Relu" output_port_id="/up2/conv/double_conv/double_conv.5/Relu_output_0" />
		<IR name="/up2/conv/double_conv/double_conv.5/Relu" output_port_id="1" />
	</map>
	<map>
		<framework name="up3.up.weight" output_port_id="up3.up.weight" />
		<IR name="up3.up.weight" output_port_id="0" />
	</map>
	<map>
		<framework name="/up3/up/ConvTranspose" output_port_id="/up3/up/ConvTranspose_output_0" />
		<IR name="/up3/up/ConvTranspose" output_port_id="2" />
	</map>
	<map>
		<framework name="/up3/Shape_2" output_port_id="/up3/Shape_output_0" />
		<IR name="/up3/Shape_2" output_port_id="1" />
	</map>
	<map>
		<framework name="/up3/Shape_2" output_port_id="/up3/Shape_2_output_0" />
		<IR name="/up3/Shape_2" output_port_id="1" />
	</map>
	<map>
		<framework name="/up3/Constant_2" output_port_id="/up3/Constant_2_output_0" />
		<IR name="/up3/Constant_2" output_port_id="0" />
	</map>
	<map>
		<framework name="/up3/Gather_2" output_port_id="/up3/Gather_2_output_0" />
		<IR name="/up3/Gather_2" output_port_id="3" />
	</map>
	<map>
		<framework name="/up3/Shape_3" output_port_id="/up3/Shape_1_output_0" />
		<IR name="/up3/Shape_3" output_port_id="1" />
	</map>
	<map>
		<framework name="/up3/Shape_3" output_port_id="/up3/Shape_3_output_0" />
		<IR name="/up3/Shape_3" output_port_id="1" />
	</map>
	<map>
		<framework name="/up3/Constant_3" output_port_id="/up3/Constant_3_output_0" />
		<IR name="/up3/Constant_3" output_port_id="0" />
	</map>
	<map>
		<framework name="/up3/Gather_3" output_port_id="/up3/Gather_3_output_0" />
		<IR name="/up3/Gather_3" output_port_id="3" />
	</map>
	<map>
		<framework name="/up3/Sub_1" output_port_id="/up3/Sub_1_output_0" />
		<IR name="/up3/Sub_1" output_port_id="2" />
	</map>
	<map>
		<framework name="/up3/Constant_4" output_port_id="/up3/Constant_4_output_0" />
		<IR name="/up3/Constant_4" output_port_id="0" />
	</map>
	<map>
		<framework name="/up3/Div" output_port_id="/up3/Cast_1_output_0" />
		<IR name="/up3/Div" output_port_id="2" />
	</map>
	<map>
		<framework name="/up3/Div" output_port_id="/up3/Div_output_0" />
		<IR name="/up3/Div" output_port_id="2" />
	</map>
	<map>
		<framework name="/up3/Div" output_port_id="/up3/Cast_output_0" />
		<IR name="/up3/Div" output_port_id="2" />
	</map>
	<map>
		<framework name="/up3/Unsqueeze_4" output_port_id="/up3/Unsqueeze_4_output_0" />
		<IR name="/up3/Unsqueeze_4" output_port_id="2" />
	</map>
	<map>
		<framework name="/up3/Sub_2" output_port_id="/up3/Sub_2_output_0" />
		<IR name="/up3/Sub_2" output_port_id="2" />
	</map>
	<map>
		<framework name="/up3/Unsqueeze_5" output_port_id="/up3/Unsqueeze_5_output_0" />
		<IR name="/up3/Unsqueeze_5" output_port_id="2" />
	</map>
	<map>
		<framework name="/up3/Constant" output_port_id="/up3/Constant_output_0" />
		<IR name="/up3/Constant" output_port_id="0" />
	</map>
	<map>
		<framework name="/up3/Gather" output_port_id="/up3/Gather_output_0" />
		<IR name="/up3/Gather" output_port_id="3" />
	</map>
	<map>
		<framework name="/up3/Constant_1" output_port_id="/up3/Constant_1_output_0" />
		<IR name="/up3/Constant_1" output_port_id="0" />
	</map>
	<map>
		<framework name="/up3/Gather_1" output_port_id="/up3/Gather_1_output_0" />
		<IR name="/up3/Gather_1" output_port_id="3" />
	</map>
	<map>
		<framework name="/up3/Sub" output_port_id="/up3/Sub_output_0" />
		<IR name="/up3/Sub" output_port_id="2" />
	</map>
	<map>
		<framework name="/up3/Constant_5" output_port_id="/up3/Constant_5_output_0" />
		<IR name="/up3/Constant_5" output_port_id="0" />
	</map>
	<map>
		<framework name="/up3/Div_1" output_port_id="/up3/Cast_3_output_0" />
		<IR name="/up3/Div_1" output_port_id="2" />
	</map>
	<map>
		<framework name="/up3/Div_1" output_port_id="/up3/Div_1_output_0" />
		<IR name="/up3/Div_1" output_port_id="2" />
	</map>
	<map>
		<framework name="/up3/Div_1" output_port_id="/up3/Cast_2_output_0" />
		<IR name="/up3/Div_1" output_port_id="2" />
	</map>
	<map>
		<framework name="/up3/Unsqueeze_6" output_port_id="/up3/Unsqueeze_6_output_0" />
		<IR name="/up3/Unsqueeze_6" output_port_id="2" />
	</map>
	<map>
		<framework name="/up3/Sub_3" output_port_id="/up3/Sub_3_output_0" />
		<IR name="/up3/Sub_3" output_port_id="2" />
	</map>
	<map>
		<framework name="/up3/Unsqueeze_7" output_port_id="/up3/Unsqueeze_7_output_0" />
		<IR name="/up3/Unsqueeze_7" output_port_id="2" />
	</map>
	<map>
		<framework name="/up3/Concat_1" output_port_id="/up3/Cast_4_output_0" />
		<IR name="/up3/Concat_1" output_port_id="4" />
	</map>
	<map>
		<framework name="/up3/Concat_1" output_port_id="/up3/Concat_1_output_0" />
		<IR name="/up3/Concat_1" output_port_id="4" />
	</map>
	<map>
		<framework name="/up3/ConstantOfShape" output_port_id="/up3/ConstantOfShape_output_0" />
		<IR name="/up3/ConstantOfShape" output_port_id="0" />
	</map>
	<map>
		<framework name="/up3/Concat_2" output_port_id="/up3/Concat_2_output_0" />
		<IR name="/up3/Concat_2" output_port_id="2" />
	</map>
	<map>
		<framework name="/up3/Constant_8" output_port_id="/up3/Constant_8_output_0" />
		<IR name="/up3/Constant_8" output_port_id="0" />
	</map>
	<map>
		<framework name="/up3/Reshape" output_port_id="/up3/Reshape_output_0" />
		<IR name="/up3/Reshape" output_port_id="2" />
	</map>
	<map>
		<framework name="/up3/Constant_10" output_port_id="/up3/Constant_10_output_0" />
		<IR name="/up3/Constant_10" output_port_id="0" />
	</map>
	<map>
		<framework name="/up3/Constant_11" output_port_id="/up3/Constant_11_output_0" />
		<IR name="/up3/Constant_11" output_port_id="0" />
	</map>
	<map>
		<framework name="/up3/Constant_12" output_port_id="/up3/Constant_12_output_0" />
		<IR name="/up3/Constant_12" output_port_id="0" />
	</map>
	<map>
		<framework name="/up3/Slice" output_port_id="/up3/Slice_output_0" />
		<IR name="/up3/Slice" output_port_id="4" />
	</map>
	<map>
		<framework name="/up3/Transpose" output_port_id="/up3/Transpose_output_0" />
		<IR name="/up3/Transpose" output_port_id="2" />
	</map>
	<map>
		<framework name="/up3/Reshape_1" output_port_id="/up3/Cast_5_output_0" />
		<IR name="/up3/Reshape_1" output_port_id="2" />
	</map>
	<map>
		<framework name="/up3/Reshape_1" output_port_id="/up3/Reshape_1_output_0" />
		<IR name="/up3/Reshape_1" output_port_id="2" />
	</map>
	<map>
		<framework name="/up3/Pad" output_port_id="/up3/Pad_output_0" />
		<IR name="/up3/Pad" output_port_id="4" />
	</map>
	<map>
		<framework name="/up3/Concat_3" output_port_id="/up3/Concat_3_output_0" />
		<IR name="/up3/Concat_3" output_port_id="2" />
	</map>
	<map>
		<framework name="onnx::Conv_377" output_port_id="onnx::Conv_377" />
		<IR name="onnx::Conv_377" output_port_id="0" />
	</map>
	<map>
		<framework name="/up3/conv/double_conv/double_conv.0/Conv" output_port_id="/up3/conv/double_conv/double_conv.0/Conv_output_0" />
		<IR name="/up3/conv/double_conv/double_conv.0/Conv" output_port_id="2" />
	</map>
	<map>
		<framework name="/up3/conv/double_conv/double_conv.2/Relu" output_port_id="/up3/conv/double_conv/double_conv.2/Relu_output_0" />
		<IR name="/up3/conv/double_conv/double_conv.2/Relu" output_port_id="1" />
	</map>
	<map>
		<framework name="onnx::Conv_380" output_port_id="onnx::Conv_380" />
		<IR name="onnx::Conv_380" output_port_id="0" />
	</map>
	<map>
		<framework name="/up3/conv/double_conv/double_conv.3/Conv" output_port_id="/up3/conv/double_conv/double_conv.3/Conv_output_0" />
		<IR name="/up3/conv/double_conv/double_conv.3/Conv" output_port_id="2" />
	</map>
	<map>
		<framework name="/up3/conv/double_conv/double_conv.5/Relu" output_port_id="/up3/conv/double_conv/double_conv.5/Relu_output_0" />
		<IR name="/up3/conv/double_conv/double_conv.5/Relu" output_port_id="1" />
	</map>
	<map>
		<framework name="outc.conv.weight" output_port_id="outc.conv.weight" />
		<IR name="outc.conv.weight" output_port_id="0" />
	</map>
	<map>
		<framework name="/outc/conv/Conv" output_port_id="/outc/conv/Conv_output_0" />
		<IR name="/outc/conv/Conv" output_port_id="2" />
	</map>
	<map>
		<framework name="output" output_port_id="output" />
		<IR name="output" output_port_id="1" />
	</map>
</mapping>
