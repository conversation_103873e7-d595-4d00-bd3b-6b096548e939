import cv2
import config

def draw_grid(img, nx=5, ny=5, size=20, color=(0, 0, 255), thickness=1):
    h = img.shape[0]
    w = img.shape[1]
    for i in range(nx+1):
        for j in range(ny+1):
            xc = (w-1)*i//nx
            yc = (h-1)*j//ny
            # cv2.line(img, (xc-hlen, yc), (xc+hlen, yc), color, thickness)
            # cv2.line(img, (xc, yc-hlen), (xc, yc+hlen), color, thickness)
            cv2.drawMarker(img, (xc, yc), color, markerSize=size, thickness=thickness)
    return img


def draw_corner(img, corner, offset=config.CORNER_OFFSET, size=20, color=(0, 0, 255)):
    h = img.shape[0]
    w = img.shape[1]
    xc = offset + (corner % 2) * (w-1-2*offset)
    yc = offset + (corner // 2) * (h-1-2*offset)
    cv2.drawMarker(img, (xc, yc), color, -1, markerSize=size, thickness=1)
    return img
