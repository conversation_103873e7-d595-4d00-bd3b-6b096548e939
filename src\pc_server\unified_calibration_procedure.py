"""
Unified calibration procedure using pygame GUI.
Shows each calibration point once and performs validation on the same data.
Stores averaged eye_center and glints in addition to gaze_origin and gaze_vec.
"""

import time
import logging
import numpy as np
import json
import datetime
import traceback
from queue import Queue, Empty as QueueEmpty
from threading import Thread
from typing import List, Optional, Dict, Any

from src.common.eye_data import FrameData, CalibrPoint, EyeData
from src.common.config import Conf
from screeninfo import Monitor
from src.common.json_encoder import <PERSON><PERSON><PERSON><PERSON>der
from src.pc_server.calibration_models import CalibrationModel
from src.pc_server.pygame_calibration_gui import PygameCalibrationGUI


class UnifiedCalibrationProcedure:
    """
    Unified calibration procedure that combines calibration and validation.
    Uses pygame for GUI and stores comprehensive eye tracking data.
    """
    
    FRAME_TIMEOUT = 0.2
    
    def __init__(self, 
                 calibration_points: List[CalibrPoint],
                 q_cal_frame_data: Queue,
                 q_cal_commands: Queue,
                 q_cal_data: Queue,
                 conf: Conf,
                 monitor: Monitor):
        """
        Initialize the unified calibration procedure.
        
        Args:
            calibration_points: List of calibration points
            q_cal_frame_data: Queue to receive frame data
            q_cal_commands: Queue to receive commands
            q_cal_data: Queue to send calibration results
            conf: Configuration object
            monitor: Monitor for display
        """
        self.conf = conf
        self.monitor = monitor
        self.calibration_points = calibration_points
        self.n_points = len(calibration_points)
        
        # Queues
        self.q_cal_frame_data = q_cal_frame_data
        self.q_cal_commands = q_cal_commands
        self.q_cal_data = q_cal_data
        
        # GUI
        self.gui = PygameCalibrationGUI(monitor)
        
        # Calibration model
        self.calibration_model = CalibrationModel(monitor, conf)
        
        # Data collection
        self.current_point_data = []
        self.all_frame_data: List[List[FrameData]] = []
        self.recorded_data = []
        
        # Timing and control
        self.start_ticks = 0
        self.start_point_time = 0
        self.skip_cnt = 0
        self.skip = 3 if self.conf['fps'] > 250 else 0

        # Time-based stimulus parameters
        self.stimulus_duration = 2.0  # Total stimulus time in seconds
        self.data_trim_time = 0.5     # Time to trim from start and end in seconds
        
        self.draw_frame_border = False
        
        # Results
        self.calibr_json = {
            'aborted': False, 
            'summary': [{}] * self.n_points, 
            'corners': [{}] * self.n_points
        }
        
    def activate_window_by_title(self):
        """Activate the calibration window by its title."""
        try:
            import pygetwindow as gw
            windows = gw.getWindowsWithTitle(self.gui.get_window_name())
            for window in windows:
                if window.title == self.gui.get_window_name():
                    for _ in range(100):
                        try:
                            window.minimize()
                            window.maximize()
                            window.activate()
                            break
                        except Exception as error:
                            time.sleep(0.1)
                            print(error)
        except Exception as error:
            logging.error(f"Error activating window: {error}")

    def check_gaze_ok(self, data: FrameData) -> bool:
        """Check if gaze data quality is acceptable."""
        for eye_data in data.eye_data:
            if (np.isnan(eye_data.gaze_origin).any() or 
                np.isnan(eye_data.gaze_vec).any()):
                return False
        return True
        
    def stimulus_completed(self) -> bool:
        """Check if stimulus duration has elapsed."""
        return time.time() - self.start_point_time >= self.stimulus_duration
        
    def numpy_gaze(self, data: FrameData) -> np.ndarray:
        """Convert FrameData to numpy array format for processing."""
        gaze = np.full((2, 2, 3), np.nan)
        for i, eye_data in enumerate(data.eye_data):
            if not np.isnan(eye_data.gaze_origin).any():
                gaze[i, 0, :] = eye_data.gaze_origin
                gaze[i, 1, :] = eye_data.gaze_vec
        return gaze
        
    def get_frame_data_with_timeout(self) -> Optional[FrameData]:
        """Get frame data from queue with timeout."""
        try:
            return self.q_cal_frame_data.get(timeout=self.FRAME_TIMEOUT)
        except QueueEmpty:
            time.sleep(self.FRAME_TIMEOUT)
            return None
            
    def process_frame_data(self, data: FrameData, waiting: bool, elapsed_time: float = 0) -> bool:
        """
        Process incoming frame data and update GUI.

        Args:
            data: Frame data to process
            waiting: Whether we're waiting for user to start point
            elapsed_time: Time elapsed since stimulus started

        Returns:
            True if data was collected, False otherwise
        """
        # Skip frames if needed for performance
        self.skip_cnt += 1
        if self.skip_cnt <= self.skip:
            return False
        self.skip_cnt = 0

        # Determine frame color based on gaze quality
        if self.check_gaze_ok(data):
            frame_color = self.gui.FRAME_OK_COLOR
            data_collected = False

            # Collect data if not waiting and within valid time window
            if (not waiting and
                self.start_ticks != 0 and
                data.ticks >= self.start_ticks and
                self.data_trim_time <= elapsed_time <= (self.stimulus_duration - self.data_trim_time)):

                # Store data with timestamp for later filtering
                data_point = {
                    'gaze_data': self.numpy_gaze(data),
                    'frame_data': data,
                    'timestamp': elapsed_time
                }
                self.current_point_data.append(data_point)
                self.all_frame_data[-1].append(data)
                data_collected = True

        else:
            frame_color = self.gui.FRAME_BAD_COLOR
            data_collected = False

        # Update GUI with frame border
        if self.draw_frame_border:
            self.gui.draw_frame_border(frame_color)

        return data_collected
        
    def calibrate_single_point(self, point_index: int, point: CalibrPoint) -> Optional[str]:
        """
        Calibrate a single point.
        
        Args:
            point_index: Index of the point being calibrated
            point: Calibration point object
            
        Returns:
            'abort' if aborted, None if successful
        """
        logging.info(f'Starting calibration point {point_index + 1}/{self.n_points}')
        
        # Initialize data collection for this point
        self.all_frame_data.append([])
        self.current_point_data = []
        
        # Clear screen and show waiting message
        self.gui.clear_screen()
        self.gui.draw_calibration_point(point.pog[0], point.pog[1])
        self.gui.draw_text(f"Point {point_index + 1}/{self.n_points} - Press SPACE to start", 
                          0.5, 0.3)
        self.gui.update_display()
        
        # Wait for user to start calibration
        while True:
            command = self.gui.handle_events()
            if command == 'abort':
                self.calibr_json['aborted'] = True
                return 'abort'
            elif command == 'start':
                self.start_ticks = time.time() * 1000  # Convert to milliseconds
                self.start_point_time = time.time()
                break

            # Process frame data while waiting
            data = self.get_frame_data_with_timeout()
            if data:
                self.process_frame_data(data, waiting=True)
                self.gui.update_display()

            self.gui.tick()

        logging.info(f'Collecting data for point {point_index + 1} with shrinking stimulus')

        # Show shrinking stimulus and collect data
        while not self.stimulus_completed():
            command = self.gui.handle_events()
            if command == 'abort':
                self.calibr_json['aborted'] = True
                return 'abort'

            # Calculate elapsed time and progress
            elapsed_time = time.time() - self.start_point_time
            progress = min(elapsed_time / self.stimulus_duration, 1.0)

            # Clear screen and draw shrinking stimulus
            self.gui.clear_screen()
            self.gui.draw_shrinking_stimulus(point.pog[0], point.pog[1], progress)

            # Show progress info
            remaining_time = max(0, self.stimulus_duration - elapsed_time)
            self.gui.draw_text(f"Point {point_index + 1}/{self.n_points} - {remaining_time:.1f}s",
                              0.5, 0.3)

            # Process frame data
            data = self.get_frame_data_with_timeout()
            if data:
                self.process_frame_data(data, waiting=False, elapsed_time=elapsed_time)

            self.gui.update_display()
            self.gui.tick()
            
        # Brief pause after collection
        time.sleep(0.5)
        
        logging.info(f'Point {point_index + 1} calibrated successfully')
        return None

    def average_eye_data(self, frame_data_list: List[FrameData]) -> Dict[str, EyeData]:
        """
        Calculate averaged eye data from frame data list.

        Args:
            frame_data_list: List of FrameData objects

        Returns:
            Dictionary with 'left' and 'right' averaged EyeData
        """
        averaged_data = {'left': EyeData(), 'right': EyeData()}

        for eye_idx, eye_name in enumerate(['left', 'right']):
            # Collect all valid data for this eye
            eye_centers = []
            glints_list = []
            gaze_origins = []
            gaze_vecs = []

            for frame_data in frame_data_list:
                eye_data = frame_data.eye_data[eye_idx]

                # Collect eye center if valid
                if not np.isnan(eye_data.eye_center).any():
                    eye_centers.append(eye_data.eye_center)

                # Collect glints if valid
                if not np.isnan(eye_data.glints).any():
                    glints_list.append(eye_data.glints)

                # Collect gaze data if valid
                if not np.isnan(eye_data.gaze_origin).any():
                    gaze_origins.append(eye_data.gaze_origin)

                if not np.isnan(eye_data.gaze_vec).any():
                    gaze_vecs.append(eye_data.gaze_vec)

            # Calculate averages
            if eye_centers:
                averaged_data[eye_name].eye_center = np.mean(eye_centers, axis=0)

            if glints_list:
                averaged_data[eye_name].glints = np.mean(glints_list, axis=0)

            if gaze_origins:
                averaged_data[eye_name].gaze_origin = np.mean(gaze_origins, axis=0)

            if gaze_vecs:
                averaged_data[eye_name].gaze_vec = np.mean(gaze_vecs, axis=0)
                # Normalize the averaged gaze vector
                norm = np.linalg.norm(averaged_data[eye_name].gaze_vec)
                if norm > 0:
                    averaged_data[eye_name].gaze_vec /= norm

        return averaged_data

    def get_averaged_calibration_point(self, point: CalibrPoint, point_index: int) -> CalibrPoint:
        """
        Calculate averaged calibration point from collected data.
        Uses all data collected during the valid time window (excluding first and last 0.5s).

        Args:
            point: Original calibration point
            point_index: Index of the point

        Returns:
            Updated calibration point with averaged data
        """
        if not self.current_point_data:
            logging.warning(f"No valid data for point {point_index}")
            return point

        # Extract frame data from the collected data points
        frame_data_list = [data_point['frame_data'] for data_point in self.current_point_data]

        logging.info(f"Point {point_index}: Using {len(frame_data_list)} samples "
                    f"from time window {self.data_trim_time}s to {self.stimulus_duration - self.data_trim_time}s")

        # Calculate averaged eye data
        averaged_eye_data = self.average_eye_data(frame_data_list)

        # Update calibration point
        updated_point = CalibrPoint()
        updated_point.pog = point.pog.copy()
        updated_point.eye_data[0] = averaged_eye_data['left']
        updated_point.eye_data[1] = averaged_eye_data['right']

        return updated_point

    def validate_calibration(self) -> str:
        """
        Validate calibration using the collected data.

        Returns:
            'ok' if validation passes, 'repeat' if it fails, 'abort' if aborted
        """
        logging.info("Starting validation on collected calibration data")

        bad_points = 0
        validation_errors = []

        # Show validation screen
        self.gui.clear_screen()
        self.gui.draw_text("Validating calibration...", 0.5, 0.5)
        self.gui.update_display()

        # Validate each calibration point using its collected data
        for i, point in enumerate(self.calibration_points):
            if i >= len(self.all_frame_data):
                continue

            frame_data_list = self.all_frame_data[i]
            if not frame_data_list:
                bad_points += 1
                continue

            # Calculate POGs for this point's data using calibration model
            point_pogs = []
            target_pog = np.array(point.pog)

            for frame_data in frame_data_list:
                # Calculate POG for each eye separately
                pog = np.zeros((2,2), dtype=float)
                self.calibration_model.calc_pog(frame_data)
                
                # Get POG from left and right eyes
                pog[0] = np.array(frame_data.eye_data[0].pog)
                pog[1] = np.array(frame_data.eye_data[1].pog)
                
                # Use average of both eyes if valid
                if not np.isnan(np.min(pog)):
                    avg_pog = np.mean(pog, axis=0)
                    point_pogs.append(avg_pog)
                    
                    # Draw validation points for visualization
                    if self.conf.get('show_validation_points', False):
                        self.gui.draw_marker(tuple(avg_pog.tolist()), 'x', size=3, color=(0, 255, 0))

            # Calculate accuracy and precision
            if point_pogs:
                point_pogs = np.array(point_pogs)
                mean_pog = np.mean(point_pogs, axis=0)
                
                # Accuracy (distance from target)
                accuracy = np.linalg.norm(target_pog - mean_pog)
                
                # Precision (RMS of distances from mean)
                distances = np.linalg.norm(point_pogs - mean_pog, axis=1)
                precision = np.sqrt(np.mean(distances**2))
                
                validation_errors.append({
                    'accuracy': accuracy,
                    'precision': precision,
                    'point_index': i
                })

            if not point_pogs:
                bad_points += 1
                continue

            # Calculate validation metrics
            point_pogs = np.array(point_pogs)
            mean_pog = np.mean(point_pogs, axis=0)
            error_vector = target_pog - mean_pog
            error_magnitude = np.linalg.norm(error_vector)

            validation_errors.append(error_magnitude)

            # Check if error exceeds threshold
            if error_magnitude > self.conf['calibr_validation_threshold']:
                bad_points += 1

            logging.info(f"Point {i+1} validation error: {error_magnitude:.3f}")

        # Show validation results
        self.gui.clear_screen()

        if bad_points == 0:
            self.gui.draw_text("Calibration Successful!", 0.5, 0.4, color=self.gui.GREEN)
            self.gui.draw_text("Press SPACE to continue or ESC to abort", 0.5, 0.6)
            result = 'ok'
        else:
            self.gui.draw_text(f"Calibration Failed: {bad_points}/{self.n_points} points",
                              0.5, 0.4, color=self.gui.RED)
            self.gui.draw_text("Press SPACE to retry or ESC to abort", 0.5, 0.6)
            result = 'repeat'

        self.gui.update_display()

        # Wait for user response
        user_response = self.gui.wait_for_space_or_escape()
        if user_response == 'abort':
            return 'abort'

        return result

    def run(self) -> Optional[List[CalibrPoint]]:
        """
        Run the complete calibration procedure.

        Returns:
            List of calibrated points if successful, None if aborted
        """
        logging.info("Starting unified calibration procedure")
        activator = None
        
        try:
            self.gui.start()

            activator = Thread(target=self.activate_window_by_title, daemon=True)
            activator.start()

            while True:
                # Reset data for new calibration attempt
                self.all_frame_data = []
                self.recorded_data = []

                # Calibrate each point
                for i, point in enumerate(self.calibration_points):
                    result = self.calibrate_single_point(i, point)
                    if result == 'abort':
                        self.record_calibration_json()
                        return None

                    # Process and store the calibrated point
                    if self.conf['send_deep_diagnostics'] != 'never':
                        # Extract gaze data for diagnostics
                        gaze_data_only = [data_point['gaze_data'] for data_point in self.current_point_data]
                        self.recorded_data.append({
                            'point': point,
                            'data': gaze_data_only,
                            'timestamps': [data_point['timestamp'] for data_point in self.current_point_data]
                        })

                    # Update calibration point with averaged data
                    self.calibration_points[i] = self.get_averaged_calibration_point(point, i)

                    # Update summary for JSON output
                    cp = self.calibration_points[i].eye_data
                    self.calibr_json['summary'][i] = {
                        'left': f'{cp[0].gaze_vec[0]:.3f}, {cp[0].gaze_vec[1]:.3f}',
                        'right': f'{cp[1].gaze_vec[0]:.3f}, {cp[1].gaze_vec[1]:.3f}'
                    }

                # Set calibration data in model
                self.calibration_model.calibr_data = self.calibration_points

                # Validate calibration
                validation_result = self.validate_calibration()

                if validation_result == 'abort':
                    self.record_calibration_json()
                    return None
                elif validation_result == 'ok':
                    # Calibration successful
                    self.record_calibration_json()
                    self.record_deep_diagnostics()
                    logging.info("Calibration completed successfully")
                    return self.calibration_points
                elif validation_result == 'repeat':
                    # Retry calibration
                    logging.info("Retrying calibration")
                    continue

        except Exception as e:
            logging.error(f"Error during calibration: {e} {traceback.format_exc()}")
            self.calibr_json['aborted'] = True
            self.record_calibration_json()
            return None
        finally:
            self.gui.stop()
            if activator is not None and activator.is_alive():
                activator.join()

    def record_calibration_json(self):
        """Record calibration results to JSON file."""
        try:
            timestamp = datetime.datetime.now().strftime('%Y_%m_%d__%H_%M_%S')
            filename = f'{self.conf["session_data_dir"]}calibr_result_{timestamp}.json'
            with open(filename, 'w') as fp:
                json.dump(self.calibr_json, fp, indent=2)
            logging.info(f"Calibration results saved to {filename}")
        except Exception as e:
            logging.error(f"Error saving calibration JSON: {e}")

    def record_deep_diagnostics(self):
        """Record detailed diagnostic data if enabled."""
        if (self.conf['send_deep_diagnostics'] != 'never' and
            len(self.recorded_data) > 0):
            try:
                timestamp = datetime.datetime.now().strftime('%Y_%m_%d__%H_%M_%S')
                filename = f'{self.conf["session_data_dir"]}deep_diag/calibr_points_{timestamp}.json'
                with open(filename, 'w') as fp:
                    json.dump(self.recorded_data, fp, indent=2, cls=JSONEncoder)
                logging.info(f"Deep diagnostics saved to {filename}")
            except Exception as e:
                logging.error(f"Error saving deep diagnostics: {e}")
