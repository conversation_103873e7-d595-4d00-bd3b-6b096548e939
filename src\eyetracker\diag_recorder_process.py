import multiprocessing as mp
import pickle
from datetime import datetime
import os
from src.common.config import Conf
from src.common.eye_data import FrameDump

class DiagRecorder:
    def __init__(self, q_diag_data:mp.Queue, conf: Conf):
        #super().__init__(name='diag_recorder', daemon=True)
        self.q_diag_data = q_diag_data
        self.conf = conf
        self.data = []
        self.full_frames = 0
        self.dir = self.conf['session_data_dir'] + 'deep_diag/'
                
    def run(self):
        os.makedirs(self.dir, exist_ok=True)
        while True:
            try:
                frame_dump: FrameDump = self.q_diag_data.get(timeout=1.)
            except:
                continue
            if frame_dump.frame is not None:
                self.full_frames += 1
                if self.full_frames > self.conf['max_full_frames_deep_diagnostics']:
                    timestamp = int(datetime.now().timestamp()*1000)
                    with open(f'{self.dir}dump_{timestamp}.pkl', 'wb') as fp:
                        pickle.dump(self.data, fp)
                    self.full_frames = 0
                    self.data = []
            self.data.append(frame_dump)
