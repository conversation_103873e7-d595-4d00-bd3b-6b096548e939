# import math
from _queue import Empty as QueueEmpty
import os
import cv2
import multiprocessing as mp
import urllib.request
import numpy as np
import json
import math
import time
import datetime
import utils
import config
import logging

logging.basicConfig(filename='demo.log', level=logging.DEBUG)


class MyChrono:
    # time in nanoseconds
    def __init__(self, name):
        self.name = name
        self.t_min = 0
        self.t_max = 0
        self.t_sum = 0
        self.n = 0

    def t_avg(self):
        if self.n > 0:
            return self.t_sum / self.n
        return 0

    def push(self, t):
        self.t_sum += t
        if t > self.t_max:
            self.t_max = t
        if t < self.t_min or self.n == 0:
            self.t_min = t
        self.n += 1

    def print_ms(self):
        print(f'{self.name}: min={self.t_min / 1e6:.2f}ms max={self.t_max / 1e6:.2f}ms avg={self.t_avg() / 1e6:.2f}ms')


class Demo:
    def __init__(self):
        self.draw_view = np.zeros((config.height, config.width, 3), dtype=np.uint8)
        self.calibr_data = np.zeros((4, 2, 3), dtype=np.float32)
        self.draw_frame = True
        self.frame = None

        self.q_data = mp.Queue()
        self.q_frames = mp.Queue()

        p = mp.Process(target=load_data, args=(self.q_data,))
        p.daemon = True
        p.start()

        p2 = mp.Process(target=load_frames, args=(self.q_frames,))
        p2.daemon = True
        p2.start()

    # draws circle if point is on screen or arrow if outside
    def draw_gaze_point(self, x, y, color=(255, 0, 0), r=30):
        h, w = self.draw_view.shape[0:2]
        if -r < x < w + r and -r < y < h + r:
            # blue = draw_view[:, :, 0]
            cv2.circle(self.draw_view, (x, y), r, color, -1)
        else:
            d = 20
            dx = 0
            dy = 0
            if x < 0:
                x = 0
                dx = -d
            if x >= w:
                x = w - 1
                dx = d
            if y < 0:
                y = 0
                dy = -d
            if y >= h:
                y = h - 1
                dy = d
            if dx != 0 and dy != 0:
                dx = int(dx * 0.7)
                dy = int(dy * 0.7)
            pts = [np.array([(x, y), (x - dx - dy, y + dx - dy), (x - dx + dy, y - dx - dy)])]
            cv2.drawContours(self.draw_view, pts, 0, color, -1)

    def calibr_tilt(self, calibr_data):
        def trace_coord_by_z(corner, coord, z):
            k = - (calibr_data[corner, 0, 2] - z) / calibr_data[corner, 1, 2]
            return calibr_data[corner, 0, coord] + k * calibr_data[corner, 1, coord]

        x = list()
        y = list()
        for i in range(4):
            x.append(trace_coord_by_z(i, 0, 0))
            y.append(trace_coord_by_z(i, 1, 0))
        dx_bot = x[3] - x[2]
        dx_top = x[1] - x[0]
        dx_top_view = calibr_data[1, 0, 0] - calibr_data[0, 0, 0]
        z_top_view = (calibr_data[0, 0, 2] + calibr_data[0, 0, 2]) / 2.
        z_top = z_top_view * (dx_top - dx_bot) / (dx_top - dx_top_view)

        # recalculate values for top corners
        for i in range(2):
            x[i] = trace_coord_by_z(i, 0, z_top)
            y[i] = trace_coord_by_z(i, 1, z_top)

        y_bot = (y[2] + y[3]) / 2.
        y_top = (y[0] + y[1]) / 2.

        # calculate perspective transform
        src = list()
        dst = list()
        h, w = self.draw_view.shape[0:2]
        s = config.CORNER_OFFSET
        for corner in range(4):
            src.append([x[corner], y[corner]])
            dst.append([float(s + (corner % 2) * (w - 1 - 2 * s)), float(s + (corner // 2) * (h - 1 - 2 * s))])

        src = np.array(src, dtype=np.float32)
        dst = np.array(dst, dtype=np.float32)
        m = cv2.getPerspectiveTransform(src, dst)
        dy = y_top - y_bot
        tilt = math.atan(-z_top / dy) * 180 / math.pi
        h = (dy * dy + z_top * z_top) ** 0.5
        print(f'tilt={tilt:.2f}deg height={h:.2f}mm (real=300mm)')

        return m, y_bot, y_top, z_top

    def get_calibr_data(self):

        def get_distance(ref, point):
            return 1 - np.dot(ref[1], point[1])
            # return np.linalg.norm(point - ref)

        def group_points(points, threshold=0.001):
            points = points.copy()
            groups = []
            while points:
                far_points = []
                ref = points.pop()
                groups.append([ref])
                for point in points:
                    d = get_distance(ref, point)
                    if d < threshold:
                        groups[-1].append(point)
                    else:
                        far_points.append(point)

                points = far_points

            # perform average operation on each group
            return groups
            # return [list(np.mean(g, axis=1).astype(int)) for g in groups]

        def record_calibr_json():
            os.makedirs('./calibr_data', exist_ok=True)
            fname = datetime.datetime.now().strftime("%Y_%m_%d__%H_%M_%S")
            fname = f'./calibr_data/{fname}.json'
            j = json.dumps(calibr_json, indent=2)
            with open(fname, 'w') as f:
                f.write(j)

        calibr_json = {'aborted': False, 'summary': ['']*4, 'corners': [{}]*4}
        for corner in [0, 1, 3, 2]:
            self.draw_view = np.zeros_like(self.draw_view)
            utils.draw_corner(self.draw_view, corner)
            cv2.imshow(config.WIN_NAME, self.draw_view)
            while True:
                while self.q_frames.qsize() > 0:
                    self.frame = self.q_frames.get()
                    self.frame = cv2.resize(self.frame, (config.width // 5, config.height // 5))

                if self.draw_frame and self.frame is not None:
                    self.draw_view[config.height * 2 // 5:config.height * 3 // 5,
                                   config.width * 2 // 5:config.width * 3 // 5, :] = self.frame

                cv2.imshow(config.WIN_NAME, self.draw_view)
                key = cv2.waitKey(10)
                if key == 32:
                    break
                if key == 27:
                    calibr_json['aborted'] = True
                    record_calibr_json()
                    return
                if key == ord('v'):
                    self.draw_frame = not self.draw_frame
                try:
                    data = self.q_data.get(timeout=0.1)
                except QueueEmpty:
                    time.sleep(1)
                    continue

                if data['gaze'] is None:
                    time.sleep(0.03)
                    continue
                coords = get_xy(data)
                s = self.draw_view.shape[1] // 2
                self.draw_view = (self.draw_view * 0.9).astype(np.uint8)
                utils.draw_corner(self.draw_view, corner)
                for eye in range(2):
                    if math.isnan(coords[eye, 0]):
                        time.sleep(0.03)
                        continue
                    x = int(coords[eye, 0])
                    y = int(coords[eye, 1])
                    self.draw_gaze_point(s + x, s - 50 + y, (0, 180, 240 * eye))
                    # cv2.circle(draw_view, (s + x, s - 50 + y), 20, (0, 180, 240 * eye), -1)
                    cv2.imshow(config.WIN_NAME, self.draw_view)
            logging.info(f'start calibrating corner {corner}')
            max_q_group = 0
            calibr_points = []
            self.draw_view = np.zeros_like(self.draw_view)
            utils.draw_corner(self.draw_view, corner)
            # cv2.waitKey(10)
            threshold = 0.005
            while max_q_group < config.N_AVG_CALIBR:
                key = cv2.waitKey(1)
                if key == 27:
                    return
                data = self.q_data.get()
                gaze = get_gaze(data)
                if gaze is None:
                    time.sleep(0.03)
                    continue
                if math.isnan(gaze[0, 1, 0]) or math.isnan(gaze[1, 1, 0]):
                    time.sleep(0.03)
                    continue
                if np.dot(gaze[0, 1], gaze[1, 1]) < 0.99:
                    continue
                calibr_points.append((gaze[0] + gaze[1]) / 2)
                if len(calibr_points) < config.N_AVG_CALIBR:
                    continue
                groups = group_points(calibr_points, threshold=threshold)
                q_group = [len(g) for g in groups]
                max_q = np.max(q_group)
                if max_q > max_q_group:
                    logging.info(f'{len(calibr_points)} points total, {max_q} points for calibration')
                    max_q_group = max_q
                logging.debug(f'Points:{len(calibr_points)} Groups:{q_group}')
            groups = group_points(calibr_points, threshold=threshold)
            q_group = [len(g) for g in groups]
            max_group = np.argmax(q_group)
            assert (len(groups[max_group]) >= config.N_AVG_CALIBR)
            avg_gaze = np.mean(groups[max_group], axis=0)
            self.calibr_data[corner] = avg_gaze
            calibr_json['summary'][corner] = f'{avg_gaze[1, 0]:.3f}, {avg_gaze[1, 1]:.3f}'
            calibr_json['corners'][corner] = \
                {
                    'points_total': len(calibr_points),
                    'points_used': len(groups[max_group]),
                    'groups_of_points': q_group,
                }
            logging.info(f'corner {corner} calibrated successfully')
        record_calibr_json()

    def demo(self):
        if os.path.isfile(config.CALIBR_FILE):
            self.calibr_data = np.load('calibr_data.npy')
        else:
            self.get_calibr_data()
            np.save('calibr_data.npy', self.calibr_data)

        M, y_bot, y_top, z_top = self.calibr_tilt(self.calibr_data)
        self.draw_view = np.zeros_like(self.draw_view)
        # h, w = self.draw_view.shape[0:2]
        points = []
        T = 0  # average time between frames
        q = 0.1  # parameter of exponential averaging
        t_prev = 0  # timestamp of previous frame
        detect = MyChrono("Detect")
        seg = MyChrono("Segmentation")
        maths = MyChrono("Math")
        total = MyChrono("Total")
        n_frames = 0
        max_frames = 300
        point = None
        logging.info('Starting main demo cycle')
        while True:
            cv2.putText(self.draw_view, f'{self.q_frames.qsize()} frames in queue, '
                                        f'{self.q_data.qsize()} jsons in queue',
                        (10, 25), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0))

            while self.q_frames.qsize() > 0:
                self.frame = self.q_frames.get()
            # if self.frame is not None:
            if self.draw_frame and self.frame is not None:
                self.frame = cv2.resize(self.frame, (config.width // 5, config.height // 5))
                self.draw_view[config.height * 2 // 5:config.height * 3 // 5,
                               config.width * 2 // 5:config.width * 3 // 5, :] = self.frame
            utils.draw_grid(self.draw_view)
            cv2.imshow(config.WIN_NAME, self.draw_view)
            self.draw_view[:, :, 1] = np.zeros_like(self.draw_view[:, :, 1])
            k = cv2.waitKey(10)
            if k == 27:
                break
            if k == 32 and point is not None:
                points.append(point)
            if k == 8:
                if len(points) > 0:
                    points.pop()
            if k == ord('c'):
                self.get_calibr_data()
                np.save('calibr_data.npy', self.calibr_data)
                M, y_bot, y_top, z_top = self.calibr_tilt(self.calibr_data)
                self.draw_view = np.zeros_like(self.draw_view)
            if k == ord('v'):
                self.draw_frame = not self.draw_frame

            try:
                data = self.q_data.get(timeout=0.1)
            except QueueEmpty:
                time.sleep(1)
                continue

            if n_frames < max_frames:
                detect.push(data['times']['detect'])
                seg.push(data['times']['segm'])
                maths.push(data['times']['math'])
                total.push(data['times']['total'])
            # if n_frames == max_frames:
            #     detect.print_ms()
            #     seg.print_ms()
            #     maths.print_ms()
            #     total.print_ms()
            n_frames += 1
            gaze = get_gaze(data)
            if gaze is None:
                time.sleep(0.03)
                continue
            avg_gaze = np.zeros((2, 3), dtype=np.float32)
            eyes = 0
            for eye in range(2):
                if math.isnan(gaze[eye, 1, 0]):
                    time.sleep(0.03)
                    continue
                avg_gaze += gaze[eye]
                eyes += 1
            if eyes == 0:
                continue
            avg_gaze /= eyes
            self.draw_view = (self.draw_view * 0.9).astype(np.uint8)
            # if eyes == 2:
            #     cos = np.dot(gaze[0, 1], gaze[1, 1])
            #     cv2.putText(draw_view, f'cos={cos:.4f}', (10, 50), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0))
            avg_point = get_intersection_point(avg_gaze, y_bot, y_top, z_top)
            avg_point = np.array([[avg_point[0:2]]], dtype=np.float32)
            dst = cv2.perspectiveTransform(avg_point, M)
            x = int(dst[0, 0, 0])
            y = int(dst[0, 0, 1])
            point = [x, y]
            self.draw_gaze_point(x, y)
            if len(points) > 0:
                cv2.drawMarker(self.draw_view, points[-1], (0, 255, 0), markerSize=3,
                               markerType=cv2.MARKER_TILTED_CROSS)
            t = data['timestamp']
            if t_prev != 0:
                if T > 0:
                    T = (1 - q) * T + q * (t - t_prev)
                else:
                    T = t - t_prev
                # cv2.putText(self.draw_view, f"server queue:{data['frames_queued']}",
                #             (10, 25), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0))
                # cv2.putText(self.draw_view, f"client queue:{self.q_data.qsize()}",
                #             (10, 50), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0))
                # cv2.putText(self.draw_view, f"framerate:{1e9 / T:.2f}fps",
                #             (10, 75), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0))
            t_prev = t

            # cv2.putText(draw_view, f'{q_data.qsize()} frames in queue', (10, 25),
            # cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0))

        if len(points) > 0:
            n = 0
            while os.path.exists(f'points_{n}.npy'):
                n += 1
            # with open(f'points_{n}.npy', 'wb') as f:
            np.save(f'points_{n}.npy', np.array(points))


def load_data(data):
    stream = urllib.request.urlopen(config.JSON_FEED_URI)
    bts = bytes()
    while True:
        bts += stream.read(128)
        # print (bytes)
        a = bts.find(b'\r\n')
        b = bts.find(b'\r\n', a + 1)
        if a == -1 or b == -1:
            continue
        j = bts[a + 2:b]
        bts = bts[b:]
        j = json.loads(j)
        if isinstance(j, str):
            j = json.loads(j)
        data.put(j)
        if data.qsize() > 5:
            data.get()


def load_frames(frames):
    stream = urllib.request.urlopen(config.VIDEO_FEED_URI)
    bts = bytes()
    while True:
        bts += stream.read(128)
        # print (bytes)
        a = bts.find(b'\xff\xd8')
        b = bts.find(b'\xff\xd9', a + 1)
        if a == -1 or b == -1:
            continue
        j = bts[a:b + 2]
        bts = bts[b + 2:]
        frames.put(cv2.imdecode(np.fromstring(j, dtype=np.uint8), cv2.IMREAD_COLOR))
        if frames.qsize() > 5:
            frames.get()


# intersection with z=0 plane
def get_xy(data):
    coords = np.full((2, 2), np.nan, dtype=np.float32)
    for eye in range(2):
        center = data['gaze'][eye]['center']
        view = data['gaze'][eye]['view']
        if math.isnan(center[0]) or math.isnan(view[0]):
            continue
        k = - center[2] / view[2]
        coords[eye, 0] = center[0] + k * view[0]
        coords[eye, 1] = center[1] + k * view[1]
    return coords


def get_gaze(data):
    if data['gaze'] is None:
        return None
    gaze = np.full((2, 2, 3), np.nan, dtype=np.float32)
    for eye in range(2):
        center = data['gaze'][eye]['center']
        view = data['gaze'][eye]['view']
        if math.isnan(center[0]) or math.isnan(view[0]):
            continue
        gaze[eye, 0] = center
        gaze[eye, 1] = view
    return gaze


def get_intersection_point(avg_view, y_bot, y_top, z_top):
    center = avg_view[0]
    view = avg_view[1]
    k = (center[2] * (y_top - y_bot) - z_top * (center[1] - y_bot)) / (z_top * view[1] - view[2] * (y_top - y_bot))
    return center + k * view


# def test_fps():
#     while q_data.qsize() > 0:
#         q_data.get()
#     t1 = time.time()
#     n = 0
#     while n < 100:
#         q_data.get()
#         n += 1
#     t2 = time.time()
#     print(t2 - t1)


def main():
    logging.info('-----App started-----')
    cv2.namedWindow(config.WIN_NAME, cv2.WINDOW_NORMAL)
    cv2.moveWindow(config.WIN_NAME, -1920, 0)
    cv2.setWindowProperty(config.WIN_NAME, cv2.WND_PROP_FULLSCREEN, cv2.WINDOW_FULLSCREEN)
    demo = Demo()
    demo.demo()


def test():
    while True:
        cv2.imshow('111', np.zeros((100, 100, 3)))
        print(cv2.waitKey())


if __name__ == '__main__':
    main()
    # test()
