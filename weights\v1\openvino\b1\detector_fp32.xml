<?xml version="1.0" ?>
<net name="torch-jit-export" version="11">
	<layers>
		<layer id="0" name="input" type="Parameter" version="opset1">
			<data shape="1,1,96,128" element_type="f32"/>
			<rt_info>
				<attribute name="fused_names" version="0" value="input"/>
			</rt_info>
			<output>
				<port id="0" precision="FP32" names="input">
					<dim>1</dim>
					<dim>1</dim>
					<dim>96</dim>
					<dim>128</dim>
				</port>
			</output>
		</layer>
		<layer id="1" name="conv1.weight" type="Const" version="opset1">
			<data element_type="f32" shape="16, 1, 5, 5" offset="0" size="1600"/>
			<rt_info>
				<attribute name="fused_names" version="0" value="conv1.weight"/>
			</rt_info>
			<output>
				<port id="0" precision="FP32" names="conv1.weight">
					<dim>16</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="2" name="Conv_0/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="2, 2" pads_end="2, 2" auto_pad="explicit"/>
			<rt_info>
				<attribute name="fused_names" version="0" value="Conv_0/WithoutBiases"/>
			</rt_info>
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>96</dim>
					<dim>128</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>16</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>96</dim>
					<dim>128</dim>
				</port>
			</output>
		</layer>
		<layer id="3" name="Reshape_41" type="Const" version="opset1">
			<data element_type="f32" shape="1, 16, 1, 1" offset="1600" size="64"/>
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="4" name="Conv_0" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<rt_info>
				<attribute name="fused_names" version="0" value="Concat_40, Conv_0, Reshape_41"/>
			</rt_info>
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>96</dim>
					<dim>128</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="24">
					<dim>1</dim>
					<dim>16</dim>
					<dim>96</dim>
					<dim>128</dim>
				</port>
			</output>
		</layer>
		<layer id="5" name="Relu_1" type="ReLU" version="opset1">
			<rt_info>
				<attribute name="fused_names" version="0" value="Relu_1"/>
			</rt_info>
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>96</dim>
					<dim>128</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="25">
					<dim>1</dim>
					<dim>16</dim>
					<dim>96</dim>
					<dim>128</dim>
				</port>
			</output>
		</layer>
		<layer id="6" name="MaxPool_2" type="MaxPool" version="opset8">
			<data strides="2, 2" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" kernel="2, 2" rounding_type="floor" auto_pad="explicit" index_element_type="i64" axis="0"/>
			<rt_info>
				<attribute name="fused_names" version="0" value="MaxPool_2"/>
			</rt_info>
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>96</dim>
					<dim>128</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="26">
					<dim>1</dim>
					<dim>16</dim>
					<dim>48</dim>
					<dim>64</dim>
				</port>
				<port id="2" precision="I64">
					<dim>1</dim>
					<dim>16</dim>
					<dim>48</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="7" name="Constant_1334" type="Const" version="opset1">
			<data element_type="f32" shape="1, 16, 1, 1" offset="1664" size="64"/>
			<rt_info>
				<attribute name="fused_names" version="0" value="BatchNormalization_3, bn1.bias, bn1.running_mean, bn1.running_var, bn1.weight"/>
			</rt_info>
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="8" name="Multiply_1590" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<rt_info>
				<attribute name="fused_names" version="0" value="BatchNormalization_3, bn1.bias, bn1.running_mean, bn1.running_var, bn1.weight"/>
			</rt_info>
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>48</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>48</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="9" name="Constant_1595" type="Const" version="opset1">
			<data element_type="f32" shape="1, 16, 1, 1" offset="1728" size="64"/>
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="10" name="BatchNormalization_3" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<rt_info>
				<attribute name="fused_names" version="0" value="BatchNormalization_3, bn1.bias, bn1.running_mean, bn1.running_var, bn1.weight"/>
			</rt_info>
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>48</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="27">
					<dim>1</dim>
					<dim>16</dim>
					<dim>48</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="11" name="conv2.weight" type="Const" version="opset1">
			<data element_type="f32" shape="32, 16, 5, 5" offset="1792" size="51200"/>
			<rt_info>
				<attribute name="fused_names" version="0" value="conv2.weight"/>
			</rt_info>
			<output>
				<port id="0" precision="FP32" names="conv2.weight">
					<dim>32</dim>
					<dim>16</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="12" name="Conv_4/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="2, 2" pads_end="2, 2" auto_pad="explicit"/>
			<rt_info>
				<attribute name="fused_names" version="0" value="Conv_4/WithoutBiases"/>
			</rt_info>
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>48</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>16</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="13" name="Reshape_92" type="Const" version="opset1">
			<data element_type="f32" shape="1, 32, 1, 1" offset="52992" size="128"/>
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="14" name="Conv_4" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<rt_info>
				<attribute name="fused_names" version="0" value="Concat_91, Conv_4, Reshape_92"/>
			</rt_info>
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="28">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="15" name="Relu_5" type="ReLU" version="opset1">
			<rt_info>
				<attribute name="fused_names" version="0" value="Relu_5"/>
			</rt_info>
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>64</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="29">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="16" name="MaxPool_6" type="MaxPool" version="opset8">
			<data strides="2, 2" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" kernel="2, 2" rounding_type="floor" auto_pad="explicit" index_element_type="i64" axis="0"/>
			<rt_info>
				<attribute name="fused_names" version="0" value="MaxPool_6"/>
			</rt_info>
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>64</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="30">
					<dim>1</dim>
					<dim>32</dim>
					<dim>24</dim>
					<dim>32</dim>
				</port>
				<port id="2" precision="I64">
					<dim>1</dim>
					<dim>32</dim>
					<dim>24</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="17" name="Constant_1460" type="Const" version="opset1">
			<data element_type="f32" shape="1, 32, 1, 1" offset="53120" size="128"/>
			<rt_info>
				<attribute name="fused_names" version="0" value="BatchNormalization_7, bn2.bias, bn2.running_mean, bn2.running_var, bn2.weight"/>
			</rt_info>
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="18" name="Multiply_1597" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<rt_info>
				<attribute name="fused_names" version="0" value="BatchNormalization_7, bn2.bias, bn2.running_mean, bn2.running_var, bn2.weight"/>
			</rt_info>
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>24</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>24</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="19" name="Constant_1602" type="Const" version="opset1">
			<data element_type="f32" shape="1, 32, 1, 1" offset="53248" size="128"/>
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="20" name="BatchNormalization_7" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<rt_info>
				<attribute name="fused_names" version="0" value="BatchNormalization_7, bn2.bias, bn2.running_mean, bn2.running_var, bn2.weight"/>
			</rt_info>
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>24</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="31">
					<dim>1</dim>
					<dim>32</dim>
					<dim>24</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="21" name="conv3.weight" type="Const" version="opset1">
			<data element_type="f32" shape="64, 32, 5, 5" offset="53376" size="204800"/>
			<rt_info>
				<attribute name="fused_names" version="0" value="conv3.weight"/>
			</rt_info>
			<output>
				<port id="0" precision="FP32" names="conv3.weight">
					<dim>64</dim>
					<dim>32</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="22" name="Conv_8/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="2, 2" pads_end="2, 2" auto_pad="explicit"/>
			<rt_info>
				<attribute name="fused_names" version="0" value="Conv_8/WithoutBiases"/>
			</rt_info>
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>24</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>32</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="23" name="Reshape_143" type="Const" version="opset1">
			<data element_type="f32" shape="1, 64, 1, 1" offset="258176" size="256"/>
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="24" name="Conv_8" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<rt_info>
				<attribute name="fused_names" version="0" value="Concat_142, Conv_8, Reshape_143"/>
			</rt_info>
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="25" name="Relu_9" type="ReLU" version="opset1">
			<rt_info>
				<attribute name="fused_names" version="0" value="Relu_9"/>
			</rt_info>
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>32</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="33">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="26" name="MaxPool_10" type="MaxPool" version="opset8">
			<data strides="2, 2" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" kernel="2, 2" rounding_type="floor" auto_pad="explicit" index_element_type="i64" axis="0"/>
			<rt_info>
				<attribute name="fused_names" version="0" value="MaxPool_10"/>
			</rt_info>
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>32</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="34">
					<dim>1</dim>
					<dim>64</dim>
					<dim>12</dim>
					<dim>16</dim>
				</port>
				<port id="2" precision="I64">
					<dim>1</dim>
					<dim>64</dim>
					<dim>12</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="27" name="Constant_1586" type="Const" version="opset1">
			<data element_type="f32" shape="1, 64, 1, 1" offset="258432" size="256"/>
			<rt_info>
				<attribute name="fused_names" version="0" value="BatchNormalization_11, bn3.bias, bn3.running_mean, bn3.running_var, bn3.weight"/>
			</rt_info>
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="28" name="Multiply_1604" type="Multiply" version="opset1">
			<data auto_broadcast="numpy"/>
			<rt_info>
				<attribute name="fused_names" version="0" value="BatchNormalization_11, bn3.bias, bn3.running_mean, bn3.running_var, bn3.weight"/>
			</rt_info>
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>12</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>12</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="29" name="Constant_1609" type="Const" version="opset1">
			<data element_type="f32" shape="1, 64, 1, 1" offset="258688" size="256"/>
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="30" name="BatchNormalization_11" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<rt_info>
				<attribute name="fused_names" version="0" value="BatchNormalization_11, bn3.bias, bn3.running_mean, bn3.running_var, bn3.weight"/>
			</rt_info>
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>12</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="35">
					<dim>1</dim>
					<dim>64</dim>
					<dim>12</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="31" name="conv4.weight" type="Const" version="opset1">
			<data element_type="f32" shape="1, 64, 1, 1" offset="258944" size="256"/>
			<rt_info>
				<attribute name="fused_names" version="0" value="conv4.weight"/>
			</rt_info>
			<output>
				<port id="0" precision="FP32" names="conv4.weight">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="32" name="Conv_12/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit"/>
			<rt_info>
				<attribute name="fused_names" version="0" value="Conv_12/WithoutBiases"/>
			</rt_info>
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>12</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>12</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="33" name="Reshape_194" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 1, 1" offset="259200" size="4"/>
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="34" name="Conv_12" type="Add" version="opset1">
			<data auto_broadcast="numpy"/>
			<rt_info>
				<attribute name="fused_names" version="0" value="Concat_193, Conv_12, Reshape_194"/>
			</rt_info>
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>12</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="36">
					<dim>1</dim>
					<dim>1</dim>
					<dim>12</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="35" name="output" type="Sigmoid" version="opset1">
			<rt_info>
				<attribute name="fused_names" version="0" value="output"/>
			</rt_info>
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>12</dim>
					<dim>16</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="output">
					<dim>1</dim>
					<dim>1</dim>
					<dim>12</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="36" name="output/sink_port_0" type="Result" version="opset1">
			<rt_info>
				<attribute name="fused_names" version="0" value="output/sink_port_0"/>
			</rt_info>
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>12</dim>
					<dim>16</dim>
				</port>
			</input>
		</layer>
	</layers>
	<edges>
		<edge from-layer="0" from-port="0" to-layer="2" to-port="0"/>
		<edge from-layer="1" from-port="0" to-layer="2" to-port="1"/>
		<edge from-layer="2" from-port="2" to-layer="4" to-port="0"/>
		<edge from-layer="3" from-port="0" to-layer="4" to-port="1"/>
		<edge from-layer="4" from-port="2" to-layer="5" to-port="0"/>
		<edge from-layer="5" from-port="1" to-layer="6" to-port="0"/>
		<edge from-layer="6" from-port="1" to-layer="8" to-port="0"/>
		<edge from-layer="7" from-port="0" to-layer="8" to-port="1"/>
		<edge from-layer="8" from-port="2" to-layer="10" to-port="0"/>
		<edge from-layer="9" from-port="0" to-layer="10" to-port="1"/>
		<edge from-layer="10" from-port="2" to-layer="12" to-port="0"/>
		<edge from-layer="11" from-port="0" to-layer="12" to-port="1"/>
		<edge from-layer="12" from-port="2" to-layer="14" to-port="0"/>
		<edge from-layer="13" from-port="0" to-layer="14" to-port="1"/>
		<edge from-layer="14" from-port="2" to-layer="15" to-port="0"/>
		<edge from-layer="15" from-port="1" to-layer="16" to-port="0"/>
		<edge from-layer="16" from-port="1" to-layer="18" to-port="0"/>
		<edge from-layer="17" from-port="0" to-layer="18" to-port="1"/>
		<edge from-layer="18" from-port="2" to-layer="20" to-port="0"/>
		<edge from-layer="19" from-port="0" to-layer="20" to-port="1"/>
		<edge from-layer="20" from-port="2" to-layer="22" to-port="0"/>
		<edge from-layer="21" from-port="0" to-layer="22" to-port="1"/>
		<edge from-layer="22" from-port="2" to-layer="24" to-port="0"/>
		<edge from-layer="23" from-port="0" to-layer="24" to-port="1"/>
		<edge from-layer="24" from-port="2" to-layer="25" to-port="0"/>
		<edge from-layer="25" from-port="1" to-layer="26" to-port="0"/>
		<edge from-layer="26" from-port="1" to-layer="28" to-port="0"/>
		<edge from-layer="27" from-port="0" to-layer="28" to-port="1"/>
		<edge from-layer="28" from-port="2" to-layer="30" to-port="0"/>
		<edge from-layer="29" from-port="0" to-layer="30" to-port="1"/>
		<edge from-layer="30" from-port="2" to-layer="32" to-port="0"/>
		<edge from-layer="31" from-port="0" to-layer="32" to-port="1"/>
		<edge from-layer="32" from-port="2" to-layer="34" to-port="0"/>
		<edge from-layer="33" from-port="0" to-layer="34" to-port="1"/>
		<edge from-layer="34" from-port="2" to-layer="35" to-port="0"/>
		<edge from-layer="35" from-port="1" to-layer="36" to-port="0"/>
	</edges>
	<meta_data>
		<MO_version value="2022.2.0-7713-af16ea1d79a-releases/2022/2"/>
		<Runtime_version value="2022.2.0-7713-af16ea1d79a-releases/2022/2"/>
		<legacy_path value="False"/>
		<cli_parameters>
			<batch value="1"/>
			<caffe_parser_path value="DIR"/>
			<compress_fp16 value="False"/>
			<data_type value="float"/>
			<disable_nhwc_to_nchw value="False"/>
			<disable_omitting_optional value="False"/>
			<disable_resnet_optimization value="False"/>
			<disable_weights_compression value="False"/>
			<enable_concat_optimization value="False"/>
			<enable_flattening_nested_params value="False"/>
			<enable_ssd_gluoncv value="False"/>
			<extensions value="DIR"/>
			<framework value="onnx"/>
			<freeze_placeholder_with_value value="{}"/>
			<input_model value="DIR\detector_fp32.onnx"/>
			<input_model_is_text value="False"/>
			<k value="DIR\CustomLayersMapping.xml"/>
			<layout value="()"/>
			<layout_values value="{}"/>
			<legacy_mxnet_model value="False"/>
			<log_level value="ERROR"/>
			<mean_scale_values value="{}"/>
			<mean_values value="()"/>
			<model_name value="detector_fp32"/>
			<output_dir value="DIR"/>
			<placeholder_data_types value="{}"/>
			<progress value="False"/>
			<remove_memory value="False"/>
			<remove_output_softmax value="False"/>
			<reverse_input_channels value="False"/>
			<save_params_from_nd value="False"/>
			<scale_values value="()"/>
			<silent value="False"/>
			<source_layout value="()"/>
			<static_shape value="False"/>
			<stream_output value="False"/>
			<target_layout value="()"/>
			<transform value=""/>
			<use_legacy_frontend value="False"/>
			<use_new_frontend value="False"/>
			<unset unset_cli_parameters="counts, disable_fusing, finegrain_fusing, input, input_checkpoint, input_meta_graph, input_proto, input_shape, input_symbol, mean_file, mean_file_offsets, nd_prefix_name, output, placeholder_shapes, pretrained_model_name, saved_model_dir, saved_model_tags, scale, tensorboard_logdir, tensorflow_custom_layer_libraries, tensorflow_custom_operations_config_update, tensorflow_object_detection_api_pipeline_config, tensorflow_use_custom_operations_config, transformations_config"/>
		</cli_parameters>
	</meta_data>
</net>
