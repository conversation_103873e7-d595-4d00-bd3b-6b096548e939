import json
import requests
import pickle
import os
import traceback

from src.common.config import Conf, default_conf


def get_settings(conf: Conf = default_conf):
    code = 0
    response = None
    resp = None
    token = None
    msg = None

    # get token from file
    if not os.path.isfile(conf['cloud_token_path']):
        msg = f"{conf['cloud_token_path']} file not found, stopping..."
        return resp, token, msg
    with open(conf['cloud_token_path']) as f:
        token = f.read()
    token = token.strip()
    
    # request to cloud
    try:
        response = requests.get(conf['cloud_host'] + conf['cloud_params_uri'],
                                headers={'Authorization': f'Bearer {token}'})
        code = response.status_code
    except:
        msg = 'Error in authorization request\n' + traceback.format_exc()
        # return resp, token, msg
    if code==403:
        msg = f"Incorrect token in file {conf['cloud_token_path']}"
    elif code==200:
        try:
            resp = json.loads(response.content)
        except:
            msg = 'Can not deserialize answer from server'
        with open(conf['cloud_settings_path'], 'wb') as fp:
            pickle.dump(resp, fp)
        if conf['debug']:
            with open(conf['cloud_settings_path'] + '.json', 'w') as fp:
                json.dump(resp, fp)
    elif os.path.isfile(conf['cloud_settings_path']):
        if response is not None:
            msg = f"Returned code {code}\n{response.text}"
        try:
            with open(conf['cloud_settings_path'], 'rb') as fp:
                resp = pickle.load(fp)
        except:
            # legacy support json cloud settings file
            with open(conf['cloud_settings_path'], 'r') as fp:
                try:
                    resp = json.load(fp)
                except:
                    msg = "Can not load settings from file"
    else:
        msg = "Can not find settings file"
    return resp, token, msg