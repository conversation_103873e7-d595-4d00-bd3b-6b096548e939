from src.common.config import *
from src.eyetracker.inference.inference_trt import InferenceTrt
# from src.eyetracker.inference.inference_onnx import InferenceOnnx

import mediapipe as mp
import cv2
import numpy as np


class BaseInference:
    def __init__(self, conf: Conf):
        self.conf = conf

    def preprocess(self, inputs):
        return inputs
        # raise NotImplementedError('Not implemented')

    def do_inference(self, inputs):
        raise NotImplementedError('Not implemented')

    def postprocess(self, outputs):
        return outputs
        # raise NotImplementedError('Not implemented')

    def inference(self, inputs):
        inputs_preprocessed = self.preprocess(inputs)
        outputs = self.do_inference(inputs_preprocessed)
        return self.postprocess(outputs)

    def release(self):
        pass


class MediaPipeDetector(BaseInference):
    # left, right, top, bottom
    LEFT_LANDMARKS = [33, 133, 159, 145]
    RIGHT_LANDMARKS = [362, 263, 386, 374]

    # center, left, right, top, bottom
    LEFT_IRIS_LANDMARKS = [468, 471, 469, 470, 472]
    RIGHT_IRIS_LANDMARKS = [473, 476, 474, 475, 477]

    def __init__(self, conf: Conf):
        super().__init__(conf)
        self.model = mp.solutions.face_mesh.FaceMesh(static_image_mode=False, max_num_faces=1, refine_landmarks=False)
        self.do_inference(np.random.randint(0, 255, (256, 256, 3), dtype=np.uint8))

    def preprocess(self, inputs):
        return cv2.cvtColor(inputs, cv2.COLOR_GRAY2RGB)

    def do_inference(self, inputs):
        return self.model.process(inputs)

    def postprocess(self, outputs):
        res = {'eye_centers': None}
        if not outputs.multi_face_landmarks:
            return res
        face_0 = outputs.multi_face_landmarks[0]
        landmarks = [
            [face_0.landmark[i] for i in self.LEFT_LANDMARKS],
            [face_0.landmark[i] for i in self.RIGHT_LANDMARKS]
        ]
        # return landmarks
        eye_centers = np.full((2, 2), np.nan)
        for eye in range(2):
            xc = (landmarks[eye][0].x + landmarks[eye][1].x) / 2
            yc = (landmarks[eye][0].y + landmarks[eye][1].y) / 2
            if 0 <= xc <= 1 and 0 <= yc <= 1:
                eye_centers[eye] = [int(xc*FRAME_WIDTH), int(yc*FRAME_HEIGHT)]
        res['eye_centers'] = eye_centers
        return res


class DeviceInference(BaseInference):
    def __init__(self, model_file, input_shape, output_shape, conf:Conf):
        super().__init__(conf)
        self.input_dims = input_shape[-1], input_shape[-2]
        self.runner = InferenceTrt (model_file, input_shape, output_shape, conf)
        self.do_inference(np.random.random(input_shape).astype(np.float32))
        self.sizes = []
        self.min_brightness = None

    def _resize_and_stack(self, inputs):
        tensor = []
        self.sizes = []
        for img in inputs:
            self.sizes.append(img.shape[0])
            resized_img = cv2.resize(img, self.input_dims, interpolation=cv2.INTER_AREA)
            resized_img = np.expand_dims(resized_img, axis=0)
            tensor.append(resized_img)
        return np.array(tensor)

    def _correct_brightness(self, inputs):
        res = []
        for img in inputs:
            mean = max(10, np.mean(img))
            if self.min_brightness is not None and mean < self.min_brightness:
                k = self.min_brightness / mean / 255.
            else:
                k = 1/255
            res.append(np.minimum(img * k, np.ones_like(img)).astype(np.float32))
        return res

    def preprocess(self, inputs):
        return self._resize_and_stack(self._correct_brightness(inputs))

    @staticmethod
    def seg_shape(ch=1, batch=BATCH_SEG):
        """
        Helper function to define the shape of segmentation inputs.

        Args:
            ch (int, optional): Number of channels. Defaults to 1.
            batch (int, optional): Batch size. Defaults to BATCH_SEG.

        Returns:
            tuple: Shape of the segmentation input as (batch, channels, height, width).
        """
        return batch, ch, HEIGHT_SEG, WIDTH_SEG

    def do_inference(self, inputs):
        return self.runner.inference(inputs)


class Detector(DeviceInference):
    def __init__(self, conf:Conf):
        super().__init__(f'detector_fp32_{WIDTH_OUT_DET}x{HEIGHT_OUT_DET}',
                         (1, 1, HEIGHT_DET, WIDTH_DET),
                         (1, 1, HEIGHT_OUT_DET, WIDTH_OUT_DET), conf)
        self.min_brightness = 64

    # def preprocess(self, inputs):
    #     # print(np.max(inputs))
    #     # cv2.imwrite('frame.jpg', inputs)
    #     return self._resize_and_stack(self._correct_brightness(inputs), WIDTH_DET, HEIGHT_DET)

    def postprocess(self, outputs):
        """
        Processes the output from the eye detector neural network to identify eye centers.

        Args:
            outputs (np.ndarray): Output array from the eye detector model.
        """
        detection = outputs[0, 0]
        det_copy = detection.copy()
        eye_centers = np.full((2,2), np.nan)  # [left/right][x/y]
        (h, w) = detection.shape
        for eye in range(2):
            # Find the coordinates with the highest detection confidence
            y, x = np.unravel_index(np.argmax(detection), detection.shape)
            if detection[y, x] < EYE_DETECTION_THRESHOLD:
                break
            x_min = x - 1 if x > 0 else x
            x_max = x + 1 if x < w - 1 else x
            y_min = y - 1 if y > 0 else y
            y_max = y + 1 if y < h - 1 else y
            s = 0.
            eye_center = np.array([0., 0.])
            for i in range(y_min, y_max + 1):
                for j in range(x_min, x_max + 1):
                    eye_center += np.array([j, i]) * detection[i, j]
                    s += detection[i, j]
                    detection[i, j] = 0.
            assert (s > 0)
            eye_center /= s
            eye_center += 0.5
            eye_center = np.multiply(eye_center, [FRAME_WIDTH / w, FRAME_HEIGHT / h])
            eye_centers[eye] = [int(eye_center[0]), int(eye_center[1])]
        if not np.isnan(np.min(eye_centers)) and eye_centers[0][0] > eye_centers[1][0]:
            eye_centers = eye_centers[::-1]
        return {'eye_centers': eye_centers, 'detection': det_copy}


class IrisSegmentator(DeviceInference):
    def __init__(self, conf:Conf):
        super().__init__(f'iris_fp32', self.seg_shape(), self.seg_shape(2), conf)
        self.min_brightness = 64

    # def preprocess(self, inputs):
    #     return self._resize_and_stack(self._correct_brightness(inputs))

    def postprocess(self, outputs):
        n = len(outputs)
        half_crop_dims = np.full(n, np.nan)
        opening_px = np.full(n, np.nan)
        iris_diam_px = np.full(n, np.nan)
        eye_center = np.full((n, 2), np.nan)
        for i in range(n):
            iris = (outputs[i] * 255.0).astype(np.uint8)
            _, iris = cv2.threshold(iris, 128, 255, cv2.THRESH_BINARY)

            for j in range(2):
                contours, _ = cv2.findContours(iris[j], cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
                if len(contours) == 0:
                    continue
                c = max(contours, key=cv2.contourArea)
                xi, yi, wi, hi = cv2.boundingRect(c)
                if j:  # sclera
                    opening_px[i] = hi * self.sizes[i] / WIDTH_SEG
                else:  # iris
                    eye_center[i][0] = int((xi + wi / 2) * self.sizes[i] / WIDTH_SEG)
                    eye_center[i][1] = int((yi + hi / 2) * self.sizes[i] / HEIGHT_SEG)
                    _, iris_radius = cv2.minEnclosingCircle(c)
                    iris_diam_px[i] = 2 * iris_radius * self.sizes[i] / WIDTH_SEG
                    # assume crops are squares, else we should replace half_size with different half_width and half_height
                    assert (WIDTH_SEG == HEIGHT_SEG and WIDTH_CROP == HEIGHT_CROP)
                    half_crop_dims[i] = int(max(wi, hi) * self.sizes[i] / WIDTH_SEG * IRIS_SCALE_FOR_CROP_FACTOR / 2)
        return {'masks': outputs, 'half_crop_dims':half_crop_dims, 'opening_px': opening_px,
                'iris_diam_px':iris_diam_px, 'eye_center': eye_center }

class PupilSegmentator(DeviceInference):
    def __init__(self, conf:Conf):
        super().__init__(f'pupil_fp32', self.seg_shape(batch=conf['pupil_batch']),
                         self.seg_shape(3, batch=conf['pupil_batch']), conf)
        self.min_brightness = 64

    # def preprocess(self, inputs):
    #     return self._resize_and_stack(self._correct_brightness(inputs))
    
    # def postprocess(self, outputs):
    #     n = len(outputs)
    #     opening_px = np.full(n, np.nan)
    #     pupil_diam_px = np.full(n, np.nan)
    #     detected_locations = np.full((n, 3, 2), np.nan)
    #     return {'masks': outputs, 'opening_px': opening_px, 'pupil_diam_px':pupil_diam_px, 'detected_locations': detected_locations }
