from collections import deque
import numpy as np

class BaseAverage:
    def reset(self):
        raise NotImplementedError

    def append(self, value:float):
        raise NotImplementedError

    @property
    def avg(self) -> float:
        raise NotImplementedError


class LinAverage(BaseAverage):
    def __init__(self, n):
        self.n = n
        self.values = deque(maxlen=self.n)

    def reset(self):
        self.values.clear()

    def append(self, value: float):
        self.values.append(value)

    @property
    def avg(self) -> float:
        return float(np.nanmean(self.values))


class MedAverage(LinAverage):
    @property
    def avg(self) -> float:
        return float(np.nanmedian(self.values))


class ExpAverage(BaseAverage):
    def __init__(self, n):
        self.q = 1./n
        self.__avg = None

    def reset(self):
        self.__avg = None

    def append(self, value: float):
        if self.__avg is None:
            self.__avg = value
        elif not np.isnan(value):
            self.__avg = (1 - self.q) * self.avg + self.q * value

    @property
    def avg(self) -> float:
        return self.__avg
