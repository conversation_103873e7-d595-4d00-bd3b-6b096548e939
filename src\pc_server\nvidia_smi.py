#!/usr/bin/env python
# -*- coding: utf8 -*-

"""
A (user-)friendly wrapper to nvidia-smi

Author: <PERSON>agiot<PERSON>
Adapted from: https://github.com/anderskm/gputil

"""

__version__ = "0.1.0"

import argparse
import json
import itertools as it
import operator
import os
import shlex
import shutil
import subprocess
import sys


NVIDIA_SMI_GET_GPUS = "nvidia-smi --query-gpu=index,name,uuid,utilization.gpu,memory.total,memory.used,memory.free,driver_version,name,gpu_serial,display_active,display_mode,temperature.gpu --format=csv,noheader,nounits"

def to_float_or_inf(value):
    try:
        number = float(value)
    except ValueError:
        number = float("nan")
    return number

def get_gpus():
    output = subprocess.check_output(shlex.split(NVIDIA_SMI_GET_GPUS))
    lines = output.decode("utf-8").split(os.linesep)
    gpus = []
    for line in lines:
        if not line.strip():
            continue
        gpus.append({})
        values = line.split(", ")
        gpus[-1]['id'] = values[0]
        gpus[-1]['name'] = values[1]
        gpus[-1]['uuid'] = values[2]
        gpus[-1]['gpu_util'] = to_float_or_inf(values[3])
        gpus[-1]['mem_total'] = to_float_or_inf(values[4])
        gpus[-1]['mem_used'] = to_float_or_inf(values[5])
        gpus[-1]['mem_free'] = to_float_or_inf(values[6])
        gpus[-1]['driver'] = values[7]
        gpus[-1]['gpu_name'] = values[8]
        gpus[-1]['serial'] = values[9]
        gpus[-1]['display_active'] = values[10]
        gpus[-1]['display_mode'] = values[11]
        gpus[-1]['temp_gpu'] = to_float_or_inf(values[12])        
    return gpus

if __name__ == "__main__":
    print(get_gpus())
