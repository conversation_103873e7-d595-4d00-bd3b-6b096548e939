#src/eyetracker/utils.py
"""
Eye Tracker Utilities Module
============================

This module provides utility functions for the eye-tracking system, including visualization
helpers and geometric computations related to gaze estimation. The functions facilitate the
processing and display of segmentation outputs, gaze visualization, and geometric calculations
for determining the point of gaze (POG) based on detected eye features.

Key Functions:
- `draw_seg_output`: Visualizes segmentation results for iris and pupil detection.
- `draw_gaze`: Draws gaze points on the frame based on detected locations.
- `get_line_distance`: Calculates the intersection point and distance between gaze lines.
- `estimate_gaze`: Estimates the 3D gaze vectors and updates frame data with eye information.

Dependencies:
- OpenCV for image processing and drawing.
- NumPy for numerical operations.
- Custom modules: `config` and `common.eye_data`.
"""
import numpy as np
import cv2
from src.common.config import *
from src.common.eye_data import FrameData


def draw_seg_output(out_frame, eye, res, crop, crop_iris, iris_contours):
    """
    Draws the segmentation output for iris and pupil detection on the output frame.

    This function visualizes the segmentation results by overlaying the cropped images and
    drawing contours around the detected iris regions.

    Args:
        out_frame (np.ndarray): The output frame where segmentation results will be drawn.
        eye (int): The eye index (e.g., 0 for left, 1 for right).
        res (np.ndarray): The segmentation result for the pupil or iris.
        crop (np.ndarray): The cropped image of the eye region.
        crop_iris (np.ndarray): The cropped image of the iris region.
        iris_contours (list): List of contours detected around the iris.

    Returns:
        np.ndarray: The updated output frame with segmentation visualizations.
    """
    # out_frame = np.zeros((3 * HEIGHT_SEG, 2 * WIDTH_SEG, 3), dtype=np.uint8)
    res = (res * 255.0).astype(np.uint8)
    res = np.transpose(res, (1, 2, 0))
    # crop = (crop * 255.0).astype(np.uint8)
    # crop_iris = (crop_iris * 255.0).astype(np.uint8)
    # Draw contours around the iris
    cv2.drawContours(crop, iris_contours, -1, (255, 255, 255), 1)
    # Overlay the cropped eye and iris images onto the output frame
    for j in range(3):
        out_frame[0:HEIGHT_SEG, eye * WIDTH_SEG:(eye + 1) * WIDTH_SEG, j] = crop[:, :]
        out_frame[HEIGHT_SEG:HEIGHT_SEG * 2, eye * WIDTH_SEG:(eye + 1) * WIDTH_SEG, j] = crop_iris[:, :]
    out_frame[HEIGHT_SEG * 2:HEIGHT_SEG * 3, eye * WIDTH_SEG:(eye + 1) * WIDTH_SEG, :] = res[:, :, :]
    return out_frame


# TODO: rewrite
def draw_gaze(frame, frame_data:FrameData, detected_locations):
    """
    Draws gaze points on the frame based on detected eye locations and gaze vectors.

    This function visualizes the gaze by drawing circles at detected locations for both eyes.
    It differentiates between different points using color coding.

    Args:
        frame (np.ndarray): The frame image where gaze points will be drawn.
        frame_data (FrameData): The frame data containing eye gaze information.
        detected_locations (np.ndarray): Array of detected eye locations.

    Returns:
        np.ndarray: The updated frame with gaze points drawn.
    """
    # return frame
    # global draw_view
    # frame = cv2.cvtColor(frame, cv2.COLOR_GRAY2BGR)
    # draw_view = draw_view * 0.9
    # draw_view = draw_view.astype(np.uint8)
    # if eye_gaze is None:
    #     return fra
    # for eye in range(2):
    #     vec = np.array(frame_data.eye_data[eyes[eye]].gaze_vec)
    #     if not np.isnan(np.min(detected_locations[eye])) and not np.isnan(np.min(vec)):
    #         x1, y1 = detected_locations[eye, 0]
    #         x1, y1 = int(x1), int(y1)
    #         cv2.line(frame, (x1, y1), (x1 + int(50*vec[0]), y1 + int(50*vec[1])), color=(0, 0, 255), thickness=2)            
    # Iterate over six points (assuming three points per eye)
    for eye in range(2):
        gaze_vec = np.array(frame_data.eye_data[eye].gaze_vec, dtype=float)
        # print(gaze_vec, type(gaze_vec), gaze_vec.dtype)
        for ch in range(3):
            color = (0, 255, 0) if ch else (0, 0, 255)
            cX, cY = detected_locations[eye, ch]
            if not np.isnan(cX) and not np.isnan(cY):
                cv2.circle(frame, (int(cX), int(cY)), 5, color, 1)
                if ch==0 and not np.isnan(gaze_vec[0]):
                    p2 = detected_locations[eye, 0] + 100*gaze_vec[:2]
                    cv2.line(frame, (int(cX), int(cY)), (int(p2[0]), int(p2[1])), (0, 0, 255), 2)
    return frame


def get_line_distance(eye_gaze):
    """
    Calculates the intersection point and distance between the gaze lines of both eyes.

    This function computes the point where the gaze vectors from both eyes intersect
    and the distance from this point to the origin. It uses least squares to solve
    for the intersection.

    Args:
        eye_gaze (list of dict): List containing gaze information for both eyes, each as a dictionary
                                  with 'view' and 'center' keys.

    Returns:
        tuple: A tuple containing the intersection point (np.ndarray) and the distance (float).
               Returns (None, None) if the calculation fails due to invalid data.
    """
    # Calculate the cross product of the gaze vectors to find a perpendicular vector
    r = np.cross(eye_gaze[0]['view'], eye_gaze[1]['view'])
    if np.isnan(r[0]):
        return None, None
    r /= np.linalg.norm(r)
    r = r * np.dot(r, eye_gaze[0]['center'] - eye_gaze[1]['center'])
    # print("dist", np.dot(r,eye_gaze[0,1] ), np.dot(r,eye_gaze[0,1] ))

    A = np.zeros((3,2))
    A[:, 0] = eye_gaze[0]['view']
    A[:, 1] = -eye_gaze[1]['view']
    b = eye_gaze[1]['center'] - eye_gaze[0]['center'] + r
    try:
        res = np.linalg.lstsq(A, b, rcond=-1)
    except:
        pass
    # print(res)
    # Calculate the average intersection point
    c = (eye_gaze[0]['center'] + eye_gaze[1]['center'] + res[0][0] * eye_gaze[0]['view'] + res[0][1] * eye_gaze[1]['view']) / 2
    return c, np.linalg.norm(r)


def fix_glares(detected_locations):
    if np.sum(detected_locations[:, 1:, 0] == 0) == 1: # one glare missed
        for eye in range(2):
            for ch in range(1,3):
                if detected_locations[eye, ch, 0] == 0:
                    bad_ch = ch
                    good_ch = 3 - bad_ch
                    bad_eye = eye
                    good_eye = 1 - eye
                    detected_locations[bad_eye, bad_ch, :] = (detected_locations[bad_eye, good_ch, :] + 
                                                              detected_locations[good_eye, bad_ch, :] - 
                                                              detected_locations[good_eye, good_ch, :])
                    break
    return detected_locations
                

def estimate_gaze(detected_locations, pupil_px, opening_px, L, conf:Conf, fd: FrameData):
    """
    Estimates the 3D gaze vectors based on detected eye locations, pupil size, and camera configuration.

    This function calculates the origin and gaze vectors for both eyes in 3D space, updating the
    frame data with the estimated parameters.

    Args:
        detected_locations (np.ndarray): Array of detected eye locations.
        pupil_px (list of float): List containing pupil diameters in pixels for both eyes.
        opening_px (list of float): List containing eye opening metrics in pixels for both eyes.
        L (float): Scaling factor related to the distance between the camera and the screen.
        conf (Conf): Configuration object containing camera and eye parameters.
        fd (FrameData): The frame data object to be updated with gaze information.

    Returns:
        FrameData: The updated frame data with estimated gaze vectors and eye metrics.
    """
    eyes = ['left', 'right']
    for eye in range(2):
        # Calculate the geometric center of the eye sphere
        if not np.isnan(np.min(detected_locations[eye])):
            # it's possible to calculate depth based on distance betwwen blinks
            # L = np.sqrt(200 * R * f / np.linalg.norm(locations[eye, 1]-locations[eye, 2]) / 2)

            # Find geometric center of the eyes sphere
            center = np.mean(detected_locations[eye, 1:], axis=0) - conf['camera_cf']
            center = np.array((*(center * L / conf['camera_f']), L))
            l = np.linalg.norm(center)
            center = center * (1 + conf['eye_R'] / l)

            # Find 3d coordinate of the iris center
            #     view = np.array((*((detected_locations[eye, 0] - cf) / f * L), L)) - center
            #     view /= np.linalg.norm(view)
            # find the depth, so the point is Dr away
            # Calculate the depth (z-coordinate) based on camera and eye parameters
            a = ((detected_locations[eye, 0, 0] - conf['camera_cf'][0]) / conf['camera_f'][0]) ** 2 + (
                    (detected_locations[eye, 0, 1] - conf['camera_cf'][1]) / conf['camera_f'][1]) ** 2 + 1
            b = (detected_locations[eye, 0, 0] - conf['camera_cf'][0]) / conf['camera_f'][0] * center[0] + \
                (detected_locations[eye, 0, 1] - conf['camera_cf'][1]) / conf['camera_f'][1] * center[1] + center[2]
            c = np.sum(np.square(center)) - conf['eye_Dr'] ** 2  # R * R # * 0.25
            z = (b - np.sqrt(b * b - a * c)) / a
            view = np.array((*((detected_locations[eye, 0] - conf['camera_cf']) / conf['camera_f'] * z), z)) - center
            view /= np.linalg.norm(view)
            # eye_gaze[eye, 0] = center
            # eye_gaze[eye, 1] = view
            # Update frame data with estimated gaze information
            fd.eye_data[eyes[eye]].gaze_origin = center
            fd.eye_data[eyes[eye]].gaze_vec = view
            fd.eye_data[eyes[eye]].pupil_diam = pupil_px[eye] / conf['camera_f'][0] * z
            fd.eye_data[eyes[eye]].opening = opening_px[eye] / conf['camera_f'][0] * z
    #     else:
    #         center = np.full((3,), None, dtype=np.float32)
    #         view = np.full((3,), None, dtype=np.float32)
    #     eye_gaze.append({'center': center, 'view': view})
    # return eye_gaze
    return fd
