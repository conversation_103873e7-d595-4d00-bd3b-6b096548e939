#src/eyetracker/eyetracker_math.py
"""

- Detects and segments eyes from video frames.
- Estimates 3D gaze vectors based on detected eye features.
- Handles inter-process communication for frame data and inference results.
- Supports data recording and visualization for diagnostics.
- Manages calibration data and gaze filtering based on system configurations.


"""
# import os
# print(os.getenv('PATH'))

from utils import *
from src.common.eye_data import *
from src.common.eye_params import EyeParams
from src.common.json_encoder import J<PERSON>NEncoder
from src.common.gaze_estimation import calculate_gaze
from src.common.chrono import Chrono
from src.eyetracker.inference.batch_inference_server import NNType, InferenceProcess

import collections
import multiprocessing as mp
from multiprocessing import shared_memory
import cv2
import numpy as np
import time
import json
import logging
import os
import typing as T
from queue import Queue
from threading import Thread

RECORD_DATA_SAMPLE = False
N_DATA_SAMPLE = 600

# logging.basicConfig(filename='server.log', level=logging.INFO,
#                     format="%(asctime)s - [%(levelname)s] -  %(name)s - %(message)s")

# EYE = ['left', 'right', 'avg']

class EyeTracker:
    """
        EyeTracker Class
        ================

        The `EyeTracker` class is the core component responsible for processing video frames to detect
        and analyze eye movements, perform gaze estimation, and manage inter-process communication.
        It integrates various subsystems including eye detection, iris and pupil segmentation, and
        data recording to facilitate real-time eye tracking and gaze analysis.

        Attributes:
            detect (bool): Flag indicating whether eye detection is active.
            eye_centers (np.ndarray): Array holding the centers of detected eyes (left and right).
            detected_eye_centers (np.ndarray): Array holding the newly detected centers of eyes.
            half_crop_dims (list): List holding half dimensions for cropping eye regions.
            frame_data (FrameData or None): Current frame data being processed.
            frame_datas (list): List holding frame data for batching or processing.
            frame_info (list): List holding frame drawing information.
            old_frame_data (FrameData or None): Previous frame data for interpolation.
            frame_id (int): Counter for frame indexing.
            loop_time (float): Timestamp for the last loop iteration.
            eye_crops (list): List holding cropped eye images for segmentation.
            frame_drawn (np.ndarray or None): Image frame with drawings (e.g., gaze points).
            inference (InferenceProcess): Instance managing inference operations.
            frame_dump (FrameDump): Instance holding detailed frame information for diagnostics.
            conf (Conf): Configuration settings for the eye-tracking system.
            draw_rate (float): Rate at which frames are drawn based on configuration.
            detect_rate (float): Rate at which eye detection is performed based on configuration.
            iris_rate (float): Rate at which iris segmentation is performed based on configuration.
            record_rate (int): Rate at which frames are recorded based on configuration.
            interp (int): Interpolation factor based on configuration.
            out_frame (np.ndarray): Image frame for output visualizations.
            data_sample (list): List holding sampled frame data for recording.
            distances (collections.deque): Deque holding recent distance measurements for filtering.
            opening (np.ndarray): Array holding current eye opening metrics.
            last_opening (np.ndarray): Array holding previous eye opening metrics.
            q_frames (mp.Queue): Queue for receiving incoming frames.
            commands (mp.Queue): Queue for receiving commands.
            inference_in (mp.Queue): Queue for sending data to the inference process.
            inference_out (mp.Queue): Queue for receiving data from the inference process.
            record_feed (mp.Queue): Queue for sending recorded frame data.
            video_feed (mp.Queue): Queue for sending video frames for streaming.
            json_feed (mp.Queue): Queue for sending JSON-formatted data.
            q_frame_data (mp.Queue): Queue for sending processed frame data.
            q_out (Queue): Internal queue for handling output data.
            frame_buffer (list): List of shared memory blocks for frame buffering.
        """

    def __init__(self,
                 q_frames: mp.Queue,
                 commands: mp.Queue,
                 inference_in: mp.Queue,
                 inference_out: mp.Queue,
                 record_feed: mp.Queue,
                 video_feed: mp.Queue,
                 json_feed: mp.Queue,
                 q_frame_data: mp.Queue,
                 # q_diag_data: mp.Queue,
                 conf: Conf
                 ):
        """
        Initializes the EyeTracker with necessary queues and configuration.

        Args:
            q_frames (mp.Queue): Queue for receiving incoming frames.
            commands (mp.Queue): Queue for receiving commands.
            inference_in (mp.Queue): Queue for sending data to the inference process.
            inference_out (mp.Queue): Queue for receiving data from the inference process.
            record_feed (mp.Queue): Queue for sending recorded frame data.
            video_feed (mp.Queue): Queue for sending video frames for streaming.
            json_feed (mp.Queue): Queue for sending JSON-formatted data.
            q_frame_data (mp.Queue): Queue for sending processed frame data.
            conf (Conf): Configuration settings for the eye-tracking system.
        """
        self.detect = True
        self.eye_centers = np.full ((2,2), np.nan)  # [left/right][x/y]
        self.detected_eye_centers = np.full ((2,2), np.nan)  # [left/right][x/y]
        # self.half_crop_iris_dims = [WIDTH_CROP//2]*2
        self.half_crop_dims = [WIDTH_CROP//2]*2
        self.frame_data: FrameData|None = None
        self.frame_datas : list[FrameData] = []
        self.frame_info: list[FrameDrawInfo] = []
        self.old_frame_data: FrameData | None = None 
        self.frame_id = 0
        self.loop_time = time.time()
        self.eye_crops = []
        self.frame_drawn = None
        self.inference = InferenceProcess(None, None, conf)
        self.frame_dump = FrameDump()
        self.conf = conf
        self.eye_params = EyeParams()
        self.draw_rate = np.ceil(np.abs(conf['fps']/conf['stream_fps']))
        self.detect_rate = np.ceil(np.abs(conf['fps']/conf['detect_fps']))
        self.iris_rate = np.ceil(np.abs(conf['fps']/conf['iris_fps']))
        self.record_rate = 0
        self.interp = conf['interp']
        self.out_frame = np.zeros((HEIGHT_SEG*3, WIDTH_SEG*2, 3), dtype=np.uint8)
        if conf['record_fps'] > 0:
            self.record_rate = self.draw_rate * np.ceil(np.abs(conf['stream_fps']/conf['record_fps']))
        self.data_sample = []
        self.distances = collections.deque(maxlen=10)
        self.opening = np.full ((2,), np.nan)
        self.last_opening = np.full ((2,), np.nan)
        
        self.q_frames = q_frames
        self.commands = commands
        self.inference_in = inference_in
        self.inference_out = inference_out
        self.record_feed = record_feed
        self.video_feed = video_feed
        self.json_feed = json_feed
        self.q_frame_data = q_frame_data
        self.q_out = Queue()

        np.seterr("ignore")
        # self.frames = Queue()
        # loader_thread = Thread(target=self.load, args=(self.loaded_frames, self.frames), daemon=True)
        # loader_thread.start()
        # Initialize shared memory buffers for frames
        self.frame_buffer: list[shared_memory.SharedMemory] = []
        for i in range(self.conf['frames_queue_len']):
            self.frame_buffer.append(shared_memory.SharedMemory(name=f'frame_{i}'))
        # TODO: Ensure shared memory is properly managed and released
    # @staticmethod
    # def load(frames_in, frames_out):
    #     while True:
    #         frame = frames_in.recv()
    #         frames_out.put(frame)
    
    def reset_values(self):
        self.eye_crops = []
        self.frame_datas = []
        self.frame_info = []

    @staticmethod
    def check_nan(arr):
        return np.isnan(np.min(arr))

    def load_data_sample(self, fn='data_sample.json'):
        j = None
        try:
            with open(fn) as f:
                j = json.load(f)
        except:
            return
        data_sample = []
        for f in j:
            fd = FrameData()
            for eye in f.eye_data:
                fd.eye_data[eye].gaze_origin = f[eye]['origin']
                fd.eye_data[eye].gaze_vec = f[eye]['gaze_vec']
            fd.ticks = f['ticks']
            fd.timestamp = f['timestamp']
            data_sample.append(fd)
        self.data_sample = data_sample

    def save_data_sample(self, fn='data_sample.json'):
        if self.data_sample is not None:
            with open(fn, 'w') as f:
                json.dump(self.data_sample, f, indent=2, cls=JSONEncoder)

    def fit_crop(self, eye: int, frame: np.ndarray, wc: int = WIDTH_CROP, hc: int | None = None):
        """
        Extracts and resizes the cropped region around a detected eye.

        Args:
            eye (int): Index of the eye (0 for left, 1 for right).
            frame (np.ndarray): The full image frame from which to crop.
            wc (int, optional): Width of the crop. Defaults to WIDTH_CROP.
            hc (int or None, optional): Height of the crop. If None, defaults to wc. Defaults to None.

        Returns:
            np.ndarray: The cropped and resized image of the eye region.
        """
        FIT = True
        x = int(self.eye_centers[eye][0])
        y = int(self.eye_centers[eye][1])
        if hc is None:
            hc = wc
        if FIT:
            # TODO when crop dimensions are odd numbers
            h, w = frame.shape[:2]
            assert wc % 2 == 0 and hc % 2 == 0
            assert wc < w and hc < h

            if x < wc // 2:
                x = wc // 2
            if x > w - wc // 2:
                x = w - wc // 2
            if y < hc // 2:
                y = hc // 2
            if y > h - hc // 2:
                y = h - hc // 2
            self.eye_centers[eye] = [x, y]
            crop = frame[y - hc // 2: y + hc // 2, x - wc // 2: x + wc // 2]
            return crop
        else:
            img2 = cv2.copyMakeBorder(frame, hc//2, hc//2, wc//2, wc//2, cv2.BORDER_CONSTANT, value=0)
            res = img2[y:y+hc, x:x+wc]
            if 0 in res.shape:
                logging.warning(f'Zero-shaped crop. {x=} {y=} {wc=} {hc=} {frame.shape=} {img2.shape=}')
            return res

    def eyes_detected(self):
        """
        Identifies which eyes (left/right) have been successfully detected.

        Returns:
            list: List containing indices of detected eyes (0 for left, 1 for right).
        """
        res = []
        for eye in range(2):
            if not self.check_nan(self.eye_centers[eye]):
                res.append(eye)
        return res

    def send_frame_data(self, frame_data:FrameData, frame_dump:FrameDump|None, frame):
        """
        Sends processed frame data to the appropriate queues for further handling.

        Args:
            frame_data (FrameData): The processed frame data containing eye information.
            frame_dump (FrameDump or None): Detailed frame dump data for diagnostics.
            frame: The visualized frame to be sent for streaming or recording.
        """
        if self.old_frame_data is not None:
            old = self.old_frame_data
            new = frame_data
            for i in range(1, self.interp+1):
                fd = FrameData()
                fd.timestamp = old.timestamp + (new.timestamp - old.timestamp) * i / (self.interp+1)
                fd.ticks = old.ticks + (new.ticks - old.ticks) * i / (self.interp+1)
                for eye in fd.eye_data:
                    o = old.eye_data[eye]
                    n = new.eye_data[eye]
                    if o.gaze_origin[0] is not None and n.gaze_origin[0] is not None:
                        fd.eye_data[eye].gaze_origin = o.gaze_origin + (n.gaze_origin - o.gaze_origin) * i / (self.interp+1)
                    if o.gaze_vec[0] is not None and n.gaze_vec[0] is not None:
                        fd.eye_data[eye].gaze_vec = o.gaze_vec + (n.gaze_vec - o.gaze_vec) * i / (self.interp+1)
                    if o.pupil_diam is not None and n.pupil_diam is not None:
                        fd.eye_data[eye].pupil_diam = o.pupil_diam + (n.pupil_diam - o.pupil_diam) * i / (self.interp+1)
                    if o.opening is not None and n.opening is not None:
                        fd.eye_data[eye].opening = o.opening + (n.opening - o.opening) * i / (self.interp+1)
                self.q_frame_data.put((fd, None, None))
        self.q_out.put((frame_data, frame_dump, frame))
        self.old_frame_data = frame_data

    def process_frame(self, pupil, frame_data: FrameData, frame_info: FrameDrawInfo, last = True):
        """
          Processes a single frame by updating eye locations, performing gaze estimation, and handling visualization.

          Args:
              pupil (np.ndarray): Pupil segmentation results.
              frame_data (FrameData): Data associated with the current frame.
              frame_info (FrameDrawInfo): Information for drawing on the frame.
              last (bool, optional): Flag indicating if this is the last frame in a batch. Defaults to True.
          """
        detected_locations = np.zeros((2, 3, 2))
        draw = frame_info.draw
        record = frame_info.record
        if self.conf['send_deep_diagnostics'] != 'never':
            self.frame_dump.eye_centers = self.eye_centers.copy()
        for eye in range(2):
            if draw:
                self.out_frame[0:HEIGHT_SEG, eye * WIDTH_SEG:(eye + 1) * WIDTH_SEG, :] = (pupil[eye]*255).astype(np.uint8).transpose((1,2,0))
            eye_center_old = np.array(frame_info.eye_centers[eye])
            half = frame_info.half_crop_dims[eye]
            for ch in range(3):
                _, dst = cv2.threshold(pupil[eye][ch, :, :], 0.5, 1, cv2.THRESH_BINARY)
                contours, _ = cv2.findContours((dst * 255).astype(np.uint8), cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
                c = []
                center = np.full((2,), np.nan)
                if len(contours) > 0:
                    c = max(contours, key=lambda v: cv2.contourArea(v))
                    M = cv2.moments(c)
                    if ch != 0 and M['m00'] != 0: # blinks
                        center[0] = M['m10'] / M['m00']
                        center[1] = M['m01'] / M['m00']
                    if ch == 0 and len(c) > 5: # pupil
                        # el = cv2.fitEllipse(c)
                        # center = np.array(el[0])
                        # diam = np.mean(el[1])
                        center, radius = cv2.minEnclosingCircle(c)
                        center = np.array(center)
                        diam = 2*radius
                        frame_data.eye_data[eye].pupil_px = diam * 2 * half / WIDTH_SEG
                detected_locations[eye, ch] = eye_center_old + (center * 2 * half / WIDTH_SEG) - half
            # if not self.check_nan(detected_locations[eye, 0]) and last:
            #     self.eye_centers[eye] = detected_locations[eye, 0]
            # exponential average for openings
            if np.isnan(self.last_opening[eye]) or np.isnan(self.opening[eye]):
                self.opening[eye] = self.last_opening[eye]
            else:
                q = 0.2
                self.opening[eye] = q * self.last_opening[eye] + (1-q) * self.opening[eye]
            frame_data.eye_data[eye].opening_px = self.opening[eye]
        if self.conf['fix_glare']:
            detected_locations = fix_glares(detected_locations)
        for eye in range(2):
            frame_data.eye_data[eye].eye_center = detected_locations[eye, 0]
            frame_data.eye_data[eye].glints = detected_locations[eye, 1:]
        if len(self.eyes_detected()) == 2 and (self.eye_centers[0] != self.eye_centers[1]).any():
            L = self.conf['eye_distance'] / np.linalg.norm((detected_locations[0, 0] - detected_locations[1, 0]) / self.conf['camera_f'])
            if not np.isinf(L):
                self.distances.append(L)
            L = np.median(self.distances)
            for eye in range(2):
                frame_data.eye_data[eye].depth = L
            # [eye, center_point | direction of gaze, xyz]
            # frame_data = estimate_gaze(detected_locations, pupil_px, self.opening, L, self.conf, frame_data)
            frame_data = calculate_gaze(self.eye_params, self.conf, frame_data)

            # if self.conf
            if self.conf['filter_bad_gaze']:
                try:
                    gaze_vec1 = frame_data.eye_data[0].gaze_vec
                    gaze_vec2 = frame_data.eye_data[1].gaze_vec
                    filter1 = gaze_vec1[0] is None or gaze_vec2[0] is None or \
                        self.check_nan(gaze_vec1) or self.check_nan(gaze_vec2)
                    filter2 = False
                    if not filter1:
                        cos = np.dot(gaze_vec1, gaze_vec2)
                        dx, dy = (gaze_vec2 - gaze_vec1)[:2]
                        filter2 = cos < self.conf['filter_bad_gaze_thr']
                        if self.conf['debug'] and draw:
                            cv2.putText(self.frame_drawn,
                                        f"{np.mean(self.out_frame[64:128, 64:128])} {cos=:.4f} {dx=:.3f} {dy=:.3f}",
                                        (10, self.frame_drawn.shape[0]-30),
                                        cv2.FONT_HERSHEY_COMPLEX,
                                        0.8,
                                        (0, 255, 0))
                    if filter1 or filter2:
                        frame_data.eye_data[0].gaze_vec = np.full((3,), np.nan)  # [None]*3
                        frame_data.eye_data[1].gaze_vec = np.full((3,), np.nan)  # [None]*3
                        if self.conf['debug'] and draw:
                            cv2.rectangle(self.frame_drawn, (0,0), self.frame_drawn.shape[1::-1], (0, 0, 255), 5)
                except Exception as e:
                    logging.exception(e)

        if RECORD_DATA_SAMPLE and len(self.data_sample) < N_DATA_SAMPLE:
            self.data_sample.append(self.frame_data)
            if len(self.data_sample) == N_DATA_SAMPLE:
                self.save_data_sample()
        
        frame_b = None
        if draw:
            h, w = self.out_frame.shape[0:2]
            self.frame_drawn[0:h, 0:w] = self.out_frame
            self.frame_drawn = draw_gaze(self.frame_drawn, frame_data, detected_locations)
            ret, buffer = cv2.imencode('.jpg', self.frame_drawn)
            frame_b = buffer.tobytes()
            while self.video_feed.qsize() > 20:
                self.video_feed.get()
            self.video_feed.put(frame_b)
            if record:
                while self.record_feed.qsize() > 20:
                    self.record_feed.get()
                self.record_feed.put(self.frame_drawn)
        frame_dump = None
        if self.conf['send_deep_diagnostics'] != 'never':
            self.frame_dump.detected_locations = detected_locations
            self.frame_dump.half_crop_dims = self.half_crop_dims
            frame_dump = self.frame_dump 
        self.send_frame_data(frame_data, frame_dump, frame_b)

    def post_process_pupil(self, pupil):
        """
        Processes the output from the pupil segmentation neural network and updates frame data.

        Args:
            pupil (np.ndarray): Output array from the pupil segmentator model.
        """
        # assert len(pupil) == BATCH_SEG
        if not (len(self.frame_info) == len(self.frame_datas) == len(pupil)//2):
            # print(len(self.frame_info), len(self.frame_datas), len(pupil)//2)
            raise AssertionError
        for frame in range(len(pupil)//2):
            last = frame == len(pupil)//2 - 1
            self.process_frame(pupil[2*frame:2*(frame+1)], self.frame_datas[frame], self.frame_info[frame], last)

    def run(self):
        """
        Starts the EyeTracker process loop.

        Continuously listens for frame data and processes each frame. Handles exceptions gracefully
        by logging them and resetting internal state as necessary.
        """
        logging.info(f'Eyetracking proc started, PID:{os.getpid()}')
        while True:
            try:
                self.do_run()
            except KeyboardInterrupt:
                raise
            except:
                logging.exception("Exception occurred")
                if self.conf['debug']:
                    raise
                self.reset_values()

    def do_run(self):
        """
        Processes a single iteration of frame handling, including detection, segmentation,
        and data management.
        """
        t_frame_total1 = time.perf_counter()
        try:
            message: tuple[int, FrameData] = self.q_frames.get(timeout=1.) #self.loaded_frames.recv()
            t_get1 = time.perf_counter()
        except:
            return
        frame_index, fd = message
        frame = np.ndarray((FRAME_HEIGHT, FRAME_WIDTH), dtype=np.uint8, buffer=self.frame_buffer[frame_index].buf)
        t_get2 = time.perf_counter()
        t_frame1 = t_get2
        if frame is None:
            return
        self.frame_data = fd
        self.frame_data.times['get'] = t_get2-t_get1
        detect = not (self.frame_id % self.detect_rate)
        iris = not (self.frame_id % self.detect_rate)
        draw = not (self.frame_id % self.draw_rate)
        record = self.record_rate and not (self.frame_id % self.record_rate)
        if self.conf['send_deep_diagnostics'] != 'never':
            self.frame_dump = FrameDump()
            self.frame_dump.ticks = self.frame_data.ticks
            self.frame_dump.timestamp = self.frame_data.timestamp
        if draw:
            self.frame_drawn = frame.copy()
            self.frame_drawn = cv2.cvtColor(self.frame_drawn, cv2.COLOR_GRAY2BGR)

        # eye detector
        if detect:
            t_det1 = time.perf_counter()
            use_old_detector = self.conf['use_old_detector']
            if self.conf['use_mediapipe']:
                eye_centers = self.inference.inference(frame, NNType.MEDIAPIPE)['eye_centers']
                if eye_centers is not None:
                    self.eye_centers = eye_centers
                    self.detected_eye_centers = self.eye_centers
                    use_old_detector = False

            if use_old_detector:
                im = cv2.resize(frame, (WIDTH_DET, HEIGHT_DET))
                res = self.inference.inference(im, NNType.DETECTOR)
                eye_centers = res['eye_centers']
                if draw and self.conf['debug']:
                    detection = (res['detection']*255).astype(np.uint8)
                    detection = cv2.resize(detection, self.frame_drawn.shape[1::-1], interpolation=cv2.INTER_NEAREST)
                    self.frame_drawn[:,:,1] = np.maximum(self.frame_drawn[:,:,1], detection)
                    self.detected_eye_centers = eye_centers
                for eye in range(2):
                    if self.check_nan(self.eye_centers[eye]) or self.check_nan(eye_centers[eye]) or \
                            np.abs(self.eye_centers[eye, 0] - eye_centers[eye, 0]) > WIDTH_CROP // 2 or \
                            np.abs(self.eye_centers[eye, 0] - eye_centers[eye, 0]) > WIDTH_CROP // 2:
                        self.eye_centers[eye] = eye_centers[eye]

            # if np.linalg.norm(self.detected_eye_centers[1]-self.detected_eye_centers[0]) < 100:
            #     # print(self.detected_eye_centers)
            #     cv2.imwrite('frame.jpg', frame)
            #     import pickle
            #     with open('frame.pkl', 'wb') as fp:
            #         pickle.dump(frame, fp)
            
            t_det2 = time.perf_counter()
            self.frame_data.times['detection'] = t_det2 - t_det1
            if self.conf['send_deep_diagnostics'] != 'never':
                self.frame_dump.frame = frame.copy()
                self.frame_dump.detected_eye_centers = self.detected_eye_centers
                
        eyes_detected = self.eyes_detected()

        # iris segmentator
        if iris and len(eyes_detected) > 0:
            t_iris1 = time.perf_counter()
            # iris_crops = []
            outputs = None
            for eye in range(2):
                crop_iris = None
                if eye in eyes_detected:
                    crop_iris = self.fit_crop(eye, frame, WIDTH_CROP)
                if crop_iris is None or 0 in crop_iris.shape:
                    crop_iris = np.zeros((WIDTH_SEG, HEIGHT_SEG))
                outputs = self.inference.inference(crop_iris, NNType.SEG_IRIS)
            assert outputs is not None
            # print(outputs['masks'].shape, outputs['masks'].max())
            for eye in range(2):
                for j in range(2):
                    self.out_frame[2 * HEIGHT_SEG:3 * HEIGHT_SEG, eye * WIDTH_SEG:(eye + 1) * WIDTH_SEG, j] \
                        = (outputs['masks'][eye, j] * 255.).astype(np.uint8)
                if outputs['half_crop_dims'][eye] > WIDTH_CROP // 8:
                    self.half_crop_dims[eye] = int(outputs['half_crop_dims'][eye])
                if not np.isnan(outputs['eye_center'][eye][0]):
                    self.eye_centers[eye] += outputs['eye_center'][eye] - WIDTH_CROP // 2
                self.last_opening[eye] = outputs['opening_px'][eye]
                 # 'iris_diam_px': iris_diam_px

            t_iris2 = time.perf_counter()
            self.frame_data.times['iris_seg'] = t_iris2 - t_iris1
        # eyes_detected = self.eyes_detected()

        # pupil segmentator
        t_pupil1 = time.perf_counter()
        pupil_res = None
        for eye in range(2):
            if eye in eyes_detected:
                if draw:
                    size = 2 * self.half_crop_dims[eye]
                    x1 = int(self.eye_centers[eye][0]) - size // 2
                    x2 = x1 + size
                    y1 = int(self.eye_centers[eye][1]) - size // 2
                    y2 = y1 + size
                    cv2.rectangle(self.frame_drawn, (x1, y1), (x2, y2), color=(0, 255, 0), thickness=2)
                    if not self.check_nan(self.detected_eye_centers[eye]):
                        size = WIDTH_CROP
                        x1 = int(self.detected_eye_centers[eye][0]) - size // 2
                        x2 = x1 + size
                        y1 = int(self.detected_eye_centers[eye][1]) - size // 2
                        y2 = y1 + size
                        cv2.rectangle(self.frame_drawn, (x1, y1), (x2, y2), color=(0, 0, 255), thickness=2)
                try:
                    crop_eye = self.fit_crop(eye, frame, 2 * self.half_crop_dims[eye])
                    if self.conf['send_deep_diagnostics'] != 'never':
                        self.frame_dump.crops[eye] = self.fit_crop(eye, frame, WIDTH_CROP)
                    crop_eye = cv2.resize(crop_eye, (WIDTH_SEG, HEIGHT_SEG))
                except:
                    crop_eye = np.zeros((WIDTH_SEG, HEIGHT_SEG), dtype=np.uint8)

                if 0 not in crop_eye.shape:
                    for j in range(3):
                        self.out_frame[HEIGHT_SEG:2*HEIGHT_SEG, eye*WIDTH_SEG:(eye+1)*WIDTH_SEG, j] = crop_eye
            else:
                crop_eye = np.zeros((WIDTH_SEG, HEIGHT_SEG), dtype=np.uint8)
            pupil_res = self.inference.inference(crop_eye, NNType.SEG_PUPIL)
        self.frame_info.append(FrameDrawInfo(self.frame_id,
                                            self.eye_centers,
                                            self.half_crop_dims,
                                            draw,
                                            record))
        self.frame_datas.append(self.frame_data)
        if pupil_res is not None:
            # self.inference_in.put((np.array(self.eye_crops), NNType.SEG_PUPIL, self.frame_id, self.frame_info))
            # out: T.Tuple[np.array, NNType, int, list[FrameInfo]|None] = self.inference_out.get()
            # outputs, nn_type, frame_id, frame_info = out
            # outputs = self.inference.inference(np.array(self.eye_crops), NNType.SEG_PUPIL)
            self.post_process_pupil(pupil_res)
            self.frame_info = []
            self.frame_datas = []
            self.eye_crops = []

        t_pupil2 = time.perf_counter()
        self.frame_data.times['pupil_seg'] = t_pupil2-t_pupil1

        t_frame2 = time.perf_counter()
        self.frame_data.times['frame']  = t_frame2 - t_frame1
        self.frame_data.times['frame_total']  = t_frame2 - t_frame_total1
        while self.q_out.qsize() > 0:
            self.q_frame_data.put(self.q_out.get())
        self.frame_id += 1
