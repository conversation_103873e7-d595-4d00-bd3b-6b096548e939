import multiprocessing as mp
import datetime
import cv2
import os
import numpy as np

class VideoRecorder:
    def __init__(self, q_video:mp.Queue, max_frames=600, fps=10, codec='XVID'):
        self.q_video = q_video
        self.fps = fps
        self.max_frames = max_frames
        self.codec = codec
        self.frame_counter = 0

        self.w = None
        self.h = None
        self.writer = None
        
    def __del__(self):
        if self.writer is not None:
            self.writer.release()
            
    def open_video_file(self):
        if self.writer is not None:
            self.writer.release()
        fname = datetime.datetime.now().strftime("./records/rec_%Y_%m_%d-%H-%M-%S.avi")
        self.writer = cv2.VideoWriter(fname, cv2.VideoWriter_fourcc(*self.codec), self.fps, (self.w, self.h))
        
    def run(self):
        while True:
            try:
                frame: np.ndarray = self.q_video.get(timeout=1.)
            except:
                continue
            if self.writer is None:
                os.makedirs('./records', exist_ok=True)
                self.h, self.w = frame.shape[0:2]
                self.open_video_file()
            self.writer.write(frame)
            self.frame_counter += 1
            if self.frame_counter >= self.max_frames:
                self.frame_counter = 0
                self.open_video_file()
