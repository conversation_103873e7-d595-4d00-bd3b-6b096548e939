@REM python -m onnxruntime.tools.make_dynamic_shape_fixed --dim_param batch_size --dim_value 2 pupil_fp32.onnx b2\\pupil_fp32.onnx
@REM python -m onnxruntime.tools.make_dynamic_shape_fixed --dim_param batch_size --dim_value 2 iris_fp32.onnx b2\\iris_fp32.onnx
@REM python -m onnxruntime.tools.make_dynamic_shape_fixed --dim_param batch_size --dim_value 1 detector_fp32_16x12.onnx b1\\detector_fp32_16x12.onnx
@REM python -m onnxruntime.tools.make_dynamic_shape_fixed --dim_param batch_size --dim_value 2 pupil_fp16.onnx b2\\pupil_fp16.onnx
@REM python -m onnxruntime.tools.make_dynamic_shape_fixed --dim_param batch_size --dim_value 2 iris_fp16.onnx b2\\iris_fp16.onnx
@REM python -m onnxruntime.tools.make_dynamic_shape_fixed --dim_param batch_size --dim_value 1 detector_fp16_16x12.onnx b1\\detector_fp16_16x12.onnx
@REM python -m onnxruntime.tools.make_dynamic_shape_fixed --dim_param batch_size --dim_value 8 pupil_fp32.onnx b8\\pupil_fp32.onnx
python -m onnxruntime.tools.make_dynamic_shape_fixed --dim_param batch_size --dim_value 2 pupil_fp32.onnx b2\\pupil_fp32.onnx
@REM python -m onnxruntime.tools.make_dynamic_shape_fixed --dim_param batch_size --dim_value 8 iris_fp32.onnx b8\\iris_fp32.onnx
@REM python -m onnxruntime.tools.make_dynamic_shape_fixed --dim_param batch_size --dim_value 1 pupil_fp16.onnx b1\\pupil_fp16.onnx
@REM python -m onnxruntime.tools.make_dynamic_shape_fixed --dim_param batch_size --dim_value 1 iris_fp16.onnx b1\\iris_fp16.onnx
pause