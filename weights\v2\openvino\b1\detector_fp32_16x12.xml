<?xml version="1.0"?>
<net name="torch_jit" version="11">
	<layers>
		<layer id="0" name="input" type="Parameter" version="opset1">
			<data shape="1,1,96,128" element_type="f32" />
			<output>
				<port id="0" precision="FP32" names="input">
					<dim>1</dim>
					<dim>1</dim>
					<dim>96</dim>
					<dim>128</dim>
				</port>
			</output>
		</layer>
		<layer id="1" name="conv1.weight" type="Const" version="opset1">
			<data element_type="f32" shape="16, 1, 5, 5" offset="0" size="1600" />
			<output>
				<port id="0" precision="FP32" names="conv1.weight">
					<dim>16</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="2" name="/conv1/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="2, 2" pads_end="2, 2" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>96</dim>
					<dim>128</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>16</dim>
					<dim>1</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>96</dim>
					<dim>128</dim>
				</port>
			</output>
		</layer>
		<layer id="3" name="Reshape_41" type="Const" version="opset1">
			<data element_type="f32" shape="1, 16, 1, 1" offset="1600" size="64" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="4" name="/conv1/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>96</dim>
					<dim>128</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/conv1/Conv_output_0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>96</dim>
					<dim>128</dim>
				</port>
			</output>
		</layer>
		<layer id="5" name="/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>96</dim>
					<dim>128</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/Relu_output_0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>96</dim>
					<dim>128</dim>
				</port>
			</output>
		</layer>
		<layer id="6" name="/MaxPool" type="MaxPool" version="opset8">
			<data strides="2, 2" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" kernel="2, 2" rounding_type="floor" auto_pad="explicit" index_element_type="i64" axis="0" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>96</dim>
					<dim>128</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/MaxPool_output_0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>48</dim>
					<dim>64</dim>
				</port>
				<port id="2" precision="I64">
					<dim>1</dim>
					<dim>16</dim>
					<dim>48</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="7" name="Constant_1657" type="Const" version="opset1">
			<data element_type="f32" shape="1, 16, 1, 1" offset="1664" size="64" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="8" name="Multiply_1913" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>48</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>48</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="9" name="Constant_1918" type="Const" version="opset1">
			<data element_type="f32" shape="1, 16, 1, 1" offset="1728" size="64" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="10" name="/bn1/BatchNormalization" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>48</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/bn1/BatchNormalization_output_0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>48</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="11" name="conv2.weight" type="Const" version="opset1">
			<data element_type="f32" shape="32, 16, 5, 5" offset="1792" size="51200" />
			<output>
				<port id="0" precision="FP32" names="conv2.weight">
					<dim>32</dim>
					<dim>16</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="12" name="/conv2/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="2, 2" pads_end="2, 2" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>48</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>16</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="13" name="Reshape_92" type="Const" version="opset1">
			<data element_type="f32" shape="1, 32, 1, 1" offset="52992" size="128" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="14" name="/conv2/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/conv2/Conv_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="15" name="/Relu_1" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>64</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/Relu_1_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="16" name="/MaxPool_1" type="MaxPool" version="opset8">
			<data strides="2, 2" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" kernel="2, 2" rounding_type="floor" auto_pad="explicit" index_element_type="i64" axis="0" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>64</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/MaxPool_1_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>24</dim>
					<dim>32</dim>
				</port>
				<port id="2" precision="I64">
					<dim>1</dim>
					<dim>32</dim>
					<dim>24</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="17" name="Constant_1783" type="Const" version="opset1">
			<data element_type="f32" shape="1, 32, 1, 1" offset="53120" size="128" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="18" name="Multiply_1920" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>24</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>24</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="19" name="Constant_1925" type="Const" version="opset1">
			<data element_type="f32" shape="1, 32, 1, 1" offset="53248" size="128" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="20" name="/bn2/BatchNormalization" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>24</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/bn2/BatchNormalization_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>24</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="21" name="conv3.weight" type="Const" version="opset1">
			<data element_type="f32" shape="64, 32, 5, 5" offset="53376" size="204800" />
			<output>
				<port id="0" precision="FP32" names="conv3.weight">
					<dim>64</dim>
					<dim>32</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</output>
		</layer>
		<layer id="22" name="/conv3/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="2, 2" pads_end="2, 2" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>24</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>32</dim>
					<dim>5</dim>
					<dim>5</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="23" name="Reshape_143" type="Const" version="opset1">
			<data element_type="f32" shape="1, 64, 1, 1" offset="258176" size="256" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="24" name="/conv3/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/conv3/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="25" name="/Relu_2" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>32</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/Relu_2_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="26" name="/MaxPool_2" type="MaxPool" version="opset8">
			<data strides="2, 2" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" kernel="2, 2" rounding_type="floor" auto_pad="explicit" index_element_type="i64" axis="0" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>32</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/MaxPool_2_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>12</dim>
					<dim>16</dim>
				</port>
				<port id="2" precision="I64">
					<dim>1</dim>
					<dim>64</dim>
					<dim>12</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="27" name="Constant_1909" type="Const" version="opset1">
			<data element_type="f32" shape="1, 64, 1, 1" offset="258432" size="256" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="28" name="Multiply_1927" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>12</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>12</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="29" name="Constant_1932" type="Const" version="opset1">
			<data element_type="f32" shape="1, 64, 1, 1" offset="258688" size="256" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="30" name="/bn3/BatchNormalization" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>12</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/bn3/BatchNormalization_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>12</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="31" name="conv4.weight" type="Const" version="opset1">
			<data element_type="f32" shape="1, 64, 1, 1" offset="258944" size="256" />
			<output>
				<port id="0" precision="FP32" names="conv4.weight">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="32" name="/conv4/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>12</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>12</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="33" name="Reshape_194" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 1, 1" offset="259200" size="4" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="34" name="/conv4/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>12</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/conv4/Conv_output_0">
					<dim>1</dim>
					<dim>1</dim>
					<dim>12</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="35" name="output" type="Sigmoid" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>12</dim>
					<dim>16</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="output">
					<dim>1</dim>
					<dim>1</dim>
					<dim>12</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="36" name="output/sink_port_0" type="Result" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>12</dim>
					<dim>16</dim>
				</port>
			</input>
		</layer>
	</layers>
	<edges>
		<edge from-layer="0" from-port="0" to-layer="2" to-port="0" />
		<edge from-layer="1" from-port="0" to-layer="2" to-port="1" />
		<edge from-layer="2" from-port="2" to-layer="4" to-port="0" />
		<edge from-layer="3" from-port="0" to-layer="4" to-port="1" />
		<edge from-layer="4" from-port="2" to-layer="5" to-port="0" />
		<edge from-layer="5" from-port="1" to-layer="6" to-port="0" />
		<edge from-layer="6" from-port="1" to-layer="8" to-port="0" />
		<edge from-layer="7" from-port="0" to-layer="8" to-port="1" />
		<edge from-layer="8" from-port="2" to-layer="10" to-port="0" />
		<edge from-layer="9" from-port="0" to-layer="10" to-port="1" />
		<edge from-layer="10" from-port="2" to-layer="12" to-port="0" />
		<edge from-layer="11" from-port="0" to-layer="12" to-port="1" />
		<edge from-layer="12" from-port="2" to-layer="14" to-port="0" />
		<edge from-layer="13" from-port="0" to-layer="14" to-port="1" />
		<edge from-layer="14" from-port="2" to-layer="15" to-port="0" />
		<edge from-layer="15" from-port="1" to-layer="16" to-port="0" />
		<edge from-layer="16" from-port="1" to-layer="18" to-port="0" />
		<edge from-layer="17" from-port="0" to-layer="18" to-port="1" />
		<edge from-layer="18" from-port="2" to-layer="20" to-port="0" />
		<edge from-layer="19" from-port="0" to-layer="20" to-port="1" />
		<edge from-layer="20" from-port="2" to-layer="22" to-port="0" />
		<edge from-layer="21" from-port="0" to-layer="22" to-port="1" />
		<edge from-layer="22" from-port="2" to-layer="24" to-port="0" />
		<edge from-layer="23" from-port="0" to-layer="24" to-port="1" />
		<edge from-layer="24" from-port="2" to-layer="25" to-port="0" />
		<edge from-layer="25" from-port="1" to-layer="26" to-port="0" />
		<edge from-layer="26" from-port="1" to-layer="28" to-port="0" />
		<edge from-layer="27" from-port="0" to-layer="28" to-port="1" />
		<edge from-layer="28" from-port="2" to-layer="30" to-port="0" />
		<edge from-layer="29" from-port="0" to-layer="30" to-port="1" />
		<edge from-layer="30" from-port="2" to-layer="32" to-port="0" />
		<edge from-layer="31" from-port="0" to-layer="32" to-port="1" />
		<edge from-layer="32" from-port="2" to-layer="34" to-port="0" />
		<edge from-layer="33" from-port="0" to-layer="34" to-port="1" />
		<edge from-layer="34" from-port="2" to-layer="35" to-port="0" />
		<edge from-layer="35" from-port="1" to-layer="36" to-port="0" />
	</edges>
	<rt_info>
		<MO_version value="2022.3.0-9052-9752fafe8eb-releases/2022/3" />
		<Runtime_version value="2022.3.0-9052-9752fafe8eb-releases/2022/3" />
		<conversion_parameters>
			<batch value="1" />
			<framework value="onnx" />
			<input_model value="DIR/detector_fp32_16x12.onnx" />
			<model_name value="detector_fp32_16x12" />
			<output_dir value="DIR" />
		</conversion_parameters>
		<legacy_frontend value="False" />
	</rt_info>
</net>
