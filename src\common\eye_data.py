import numpy as np
from src.common.config import WIDTH_CROP

class EyeData:
    def __init__(self):
        self.eye_center = np.full((2,), np.nan)  # 2d point of eye center in frame
        self.glints = np.full((2,2), np.nan)  # 2d points of glints in frame
        self.depth = np.nan  # z coordinate of eye center in UCS, mm
        self.gaze_origin = np.full((3,), np.nan) # 3d point of gaze origin in UCS
        self.gaze_vec = np.full((3,), np.nan)  # 3d unity vector of gaze in UCS
        self.pupil_px = np.nan  # pupil diameter, px
        self.opening_px = np.nan  # eye opening, px
        self.pog = np.full((2,), np.nan)  # 2d point of gaze in ADCS
        self.pupil_diam = np.nan  # pupil diameter, mm
        self.opening = np.nan  # eye opening, mm


# class CalculatedEyeData(EyeData):
#     def __init__(self):
#         super().__init__()
#         self.pog = [None]*2  # 2d point of gaze in ADCS
#         self.pupil_diam = None  # pupil diameter, mm
#         self.opening = None  # eye opening, mm


class FrameData:
    def __init__(self):
        self.eye_data: list[EyeData] = [EyeData(), EyeData()]
        self.timestamp = None  # seconds
        self.ticks = None
        self.times = {}
        self.timestamps = {}

        
class FrameDrawInfo:
    def __init__(self, frame_id, eye_centers, half_crop_dims, draw, record):
        self.frame_id: int = frame_id
        # self.timestamp: float = timestamp
        # self.ticks: int = ticks
        self.eye_centers: list[list[float|None]] = eye_centers.copy()
        self.half_crop_dims: list[int] = half_crop_dims.copy()
        self.draw: bool = draw
        self.record: bool = record


class FrameDump:
    def __init__(self):
        self.frame_id: int = 0
        self.timestamp: float = 0.
        self.ticks: int = 0
        self.frame = None
        self.crops = [None]*2
        self.eye_centers = np.full ((2,2), np.nan)  # [left/right][x/y]
        self.detected_eye_centers = np.full ((2,2), np.nan)  # [left/right][x/y]
        self.half_crop_dims = [WIDTH_CROP//2]*2
        self.frame_data: FrameData | None = None  # FrameData()
        self.detected_locations = np.zeros((2, 3, 2))


class CalibrPoint:
    # uses averaged origin, gaze_vec, eye_center, and glints, calibration point as pog
    def __init__(self):
        self.eye_data = [EyeData(), EyeData()]
        self.pog = [None]*2  # 2d point of gaze in ADCS
