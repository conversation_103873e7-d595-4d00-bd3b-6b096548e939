# TODO documentation

import numpy as np
import warnings

from src.common.config import Conf
from src.common.eye_params import EyeParams
from src.common.eye_data import FrameData


def normalized(v):
    return v / np.linalg.norm(v)


def check_nan(arr:np.array):
    return np.isnan(np.min(arr))


def calculate_iota(pupil_por_wcs, center_of_cornea, R, n1, n2=1, camera_position=np.zeros(3)):
    zeta = normalized(camera_position - pupil_por_wcs)
    eta = (pupil_por_wcs - center_of_cornea) / R
    eta_dot_zeta = np.dot(eta, zeta)

    a = eta_dot_zeta - np.sqrt((n1 / n2) ** 2 - 1 + eta_dot_zeta ** 2)
    return (n2 / n1) * (a * eta - zeta)


def calculate_kr(image_pupil_center, cornea_center, R, camera_position=np.zeros(3)):
    a = np.dot(camera_position - image_pupil_center, camera_position - image_pupil_center)
    b = np.dot(camera_position - image_pupil_center, camera_position - cornea_center)
    c = np.dot(camera_position - cornea_center, camera_position - cornea_center) - R ** 2

    return (-b - np.sqrt(b ** 2 - a * c)) / a


def calculate_r(pupil_image_wcs, cornea_wcs, R, camera_position=np.zeros(3)):
    kr = calculate_kr(pupil_image_wcs, cornea_wcs, R, camera_position)
    return camera_position + kr * (camera_position - pupil_image_wcs)


def calculate_p(pupil_por_wcs, center_of_cornea, R, K, n1, n2=1, camera_position=np.zeros(3)):
    iota = calculate_iota(pupil_por_wcs, center_of_cornea, R, n1, n2, camera_position)
    rc_dot_iota = np.dot(pupil_por_wcs - center_of_cornea, iota)
    kp = -rc_dot_iota - np.sqrt(rc_dot_iota ** 2 - (R ** 2 - K ** 2))
    return pupil_por_wcs + kp * iota


def calculate_pupil_center(pupil_wcs, center_of_cornea, R, K, n1, n2=1, camera_position=np.zeros(3),
                                     use_chen_noise_reduction=False):
    pupil_por_wcs = calculate_r(pupil_wcs, center_of_cornea, R, camera_position)
    pupil_center_wcs = calculate_p(pupil_por_wcs, center_of_cornea, R, K, n1, n2, camera_position)

    if use_chen_noise_reduction:
        cxpx = center_of_cornea[0] - pupil_center_wcs[0]
        cypy = center_of_cornea[1] - pupil_center_wcs[1]
        pupil_center_wcs[2] = center_of_cornea[2] - np.sqrt(K ** 2 - cxpx ** 2 - cypy ** 2)

    return pupil_center_wcs


def calculate_q(q2d, z:float, conf: Conf):
    """
    Calculates 3d position of glint from its frame coordinates and depth
    Args:
        q2d: glint coordinates on frame (pixels)
        z: depth (mm)
        conf: configuration containing camera params
    """
    q = np.full(3, z)
    q[:2] = (q2d - conf['camera_cf']) / conf['camera_f'] * z
    return q


def calculate_cornea_center(q, light, R, camera_position=np.zeros(3)):
    l_q_unit = normalized(light - q)
    o_q_unit = normalized(camera_position - q)
    return q - R * normalized(l_q_unit + o_q_unit)


def calculate_gaze(eye_params:EyeParams, conf:Conf, fd:FrameData):
    # print(f"{depth=}")
    for eye in range(2):
        if check_nan(fd.eye_data[eye].eye_center):
            continue
        cornea_center = np.full((2,3), np.nan)
        for glint in range(2):
            if check_nan(fd.eye_data[eye].glints[glint]):
                continue
            light = conf['led_coords'].copy()
            if glint == 0:
                light[0] = -light[0]
            q = calculate_q(fd.eye_data[eye].glints[glint], fd.eye_data[eye].depth, conf)
            cornea_center[glint] = calculate_cornea_center(q, light, eye_params.R[eye])
        with warnings.catch_warnings():
            warnings.simplefilter("ignore", category=RuntimeWarning)
            cornea_center = np.nanmean(cornea_center, axis=0)
            
        # TODO average glints weighting less closer to iris contour
        if check_nan(cornea_center):
            continue
        pupil_center_on_matrix = np.ones(3)
        pupil_center_on_matrix[:2]  = (fd.eye_data[eye].eye_center - conf['camera_cf']) / conf['camera_f']
        pupil_center_on_matrix *= -conf['camera_focal_length_mm']
        pupil_center = calculate_pupil_center(pupil_center_on_matrix,
                                              cornea_center,
                                              eye_params.R[eye],
                                              eye_params.K[eye],
                                              eye_params.n)
        if not check_nan(pupil_center):
            fd.eye_data[eye].gaze_origin = pupil_center
            fd.eye_data[eye].gaze_vec = normalized (pupil_center - cornea_center)
        fd.eye_data[eye].pupil_diam = fd.eye_data[eye].pupil_px / conf['camera_f'][0] * fd.eye_data[eye].depth
        fd.eye_data[eye].opening = fd.eye_data[eye].opening_px / conf['camera_f'][0] * fd.eye_data[eye].depth
    return fd
