from numpy import pi

class EyeParams:
    def __init__(self):
        self.R = [7.8] * 2 # cornea radius
        self.K = [3.6] * 2 # distance between cornea center of curvature and pupil center
        self.n = 1.3375 # refractive index of front eye camera
        self.iris_diam = [12] * 2 # iris diameter
        self.alpha = [-5 * pi / 180] * 2 # horizontal angle between optical and view axes of eye
        self.beta = [1.5 * pi / 180] * 2 # vertical angle between optical and view axes of eye
        self.pupil_distance = 60 # distance between pupil centers
