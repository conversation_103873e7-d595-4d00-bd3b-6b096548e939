import requests
import re
import time

BASE_URL = 'http://127.0.0.1:4242'


def send_command(cmd:bytes):
    response = requests.post(BASE_URL+'/commands', params={'command': cmd + b'\r\n'})
    return response.content


def receive_records():
    last_id = 0
    while True:
        response = requests.get(BASE_URL+'/records', params={'ack_number': last_id})
        lines = response.content.split(b'\n\r')

        for l in lines:
            if len(l) == 0:
                continue
            res = re.search(b'CNT=\"([0-9]*)\"', l)
            if res is not None:
                last_id = int(res.group(1))
            print(l)
        time.sleep(1)
        
            
if __name__ == '__main__':
    commands = [
        b'<SET ID="ENABLE_SEND_COUNTER" STATE="1"/>',
        b'<SET ID="ENABLE_SEND_TIME" STATE="1" />',
        b'<SET ID="ENABLE_SEND_EYE_LEFT" STATE="1" />',
        b'<SET ID="ENABLE_SEND_EYE_RIGHT" STATE="1" />',
        b'<SET ID="ENABLE_SEND_POG_LEFT" STATE="1" />',
        b'<SET ID="ENABLE_SEND_POG_BEST" STATE="1" />',
        b'<SET ID="ENABLE_SEND_POG_RIGHT" STATE="1" />',
        b'<SET ID="ENABLE_SEND_PUPILMM" STATE="1" />',
        b'<SET ID="ENABLE_SEND_OPENMM" STATE="1" />',
        b'<SET ID="ENABLE_SEND_DATA" STATE="1" />',
        b'<SET ID="CALIBRATE_START" STATE="1" />',
    ]
    for c in commands:
        print(send_command(c))
    receive_records()
