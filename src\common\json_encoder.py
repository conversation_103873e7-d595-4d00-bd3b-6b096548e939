import json
import numpy as np
from src.common.eye_data import CalibrPoint, FrameData
from dataclasses import asdict

class JSONEncoder(json.JSONEncoder):
    """ Special json encoder for numpy types """
    def default(self, obj):
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, CalibrPoint) or isinstance(obj, FrameData): # TODO: test
            res = asdict(obj)
            for eye, data in enumerate(res['eye_data']):
                res[eye] = asdict(data)
            return res
        # if isinstance(obj, FrameData):
        #     res = {}
        #     for eye, data in obj.eye_data.items():
        #         res[eye] = {'origin': data.gaze_origin, 'gaze_vec': data.gaze_vec}
        #     res['timestamp'] = obj.timestamp
        #     res['ticks'] = obj.ticks
        #     return res
        return json.JSONEncoder.default(self, obj)