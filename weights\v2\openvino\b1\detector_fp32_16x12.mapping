<?xml version="1.0"?>
<mapping>
	<map>
		<framework name="input" output_port_id="input" />
		<IR name="input" output_port_id="0" />
	</map>
	<map>
		<framework name="conv1.weight" output_port_id="conv1.weight" />
		<IR name="conv1.weight" output_port_id="0" />
	</map>
	<map>
		<framework name="/conv1/Conv" output_port_id="/conv1/Conv_output_0" />
		<IR name="/conv1/Conv" output_port_id="2" />
	</map>
	<map>
		<framework name="/Relu" output_port_id="/Relu_output_0" />
		<IR name="/Relu" output_port_id="1" />
	</map>
	<map>
		<framework name="/MaxPool" output_port_id="/MaxPool_output_0" />
		<IR name="/MaxPool" output_port_id="1" />
	</map>
	<map>
		<framework name="/bn1/BatchNormalization" output_port_id="/bn1/BatchNormalization_output_0" />
		<IR name="/bn1/BatchNormalization" output_port_id="2" />
	</map>
	<map>
		<framework name="conv2.weight" output_port_id="conv2.weight" />
		<IR name="conv2.weight" output_port_id="0" />
	</map>
	<map>
		<framework name="/conv2/Conv" output_port_id="/conv2/Conv_output_0" />
		<IR name="/conv2/Conv" output_port_id="2" />
	</map>
	<map>
		<framework name="/Relu_1" output_port_id="/Relu_1_output_0" />
		<IR name="/Relu_1" output_port_id="1" />
	</map>
	<map>
		<framework name="/MaxPool_1" output_port_id="/MaxPool_1_output_0" />
		<IR name="/MaxPool_1" output_port_id="1" />
	</map>
	<map>
		<framework name="/bn2/BatchNormalization" output_port_id="/bn2/BatchNormalization_output_0" />
		<IR name="/bn2/BatchNormalization" output_port_id="2" />
	</map>
	<map>
		<framework name="conv3.weight" output_port_id="conv3.weight" />
		<IR name="conv3.weight" output_port_id="0" />
	</map>
	<map>
		<framework name="/conv3/Conv" output_port_id="/conv3/Conv_output_0" />
		<IR name="/conv3/Conv" output_port_id="2" />
	</map>
	<map>
		<framework name="/Relu_2" output_port_id="/Relu_2_output_0" />
		<IR name="/Relu_2" output_port_id="1" />
	</map>
	<map>
		<framework name="/MaxPool_2" output_port_id="/MaxPool_2_output_0" />
		<IR name="/MaxPool_2" output_port_id="1" />
	</map>
	<map>
		<framework name="/bn3/BatchNormalization" output_port_id="/bn3/BatchNormalization_output_0" />
		<IR name="/bn3/BatchNormalization" output_port_id="2" />
	</map>
	<map>
		<framework name="conv4.weight" output_port_id="conv4.weight" />
		<IR name="conv4.weight" output_port_id="0" />
	</map>
	<map>
		<framework name="/conv4/Conv" output_port_id="/conv4/Conv_output_0" />
		<IR name="/conv4/Conv" output_port_id="2" />
	</map>
	<map>
		<framework name="output" output_port_id="output" />
		<IR name="output" output_port_id="1" />
	</map>
</mapping>
