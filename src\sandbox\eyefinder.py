import cv2
import numpy as np
import mediapipe as mp
import time

# Initialize MediaPipe Face Mesh with additional options
mp_face_mesh = mp.solutions.face_mesh
face_mesh = mp_face_mesh.FaceMesh(static_image_mode=False, max_num_faces=1, refine_landmarks=True)

def crop_eye(image, eye_landmarks):
    x_coords = [int(landmark.x * image.shape[1]) for landmark in eye_landmarks]
    y_coords = [int(landmark.y * image.shape[0]) for landmark in eye_landmarks]

    x_min = max(0, min(x_coords) - 5)
    y_min = max(0, min(y_coords) - 5)
    x_max = min(image.shape[1], max(x_coords) + 5)
    y_max = min(image.shape[0], max(y_coords) + 5)

    if float('nan') in x_coords:
        a = 0
    eye_crop = image[y_min:y_max, x_min:x_max]
    bbox = (x_min, y_min, x_max, y_max)
    return eye_crop, bbox

# Open the webcam
cap = cv2.VideoCapture('../../../sample__.avi')

if not cap.isOpened():
    print("Error: Could not open webcam.")
    exit()

while True:
    start_time = time.time()

    ret, frame = cap.read()
    time.sleep(0.1)

    if not ret:
        cap.release()
        cap = cv2.VideoCapture('sample.avi')
        # print("Error: Could not read frame from webcam.")
        # break

    image_height, image_width = frame.shape[:2]
    frame = frame[:, ::-1, :].copy()
    rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    rgb_frame = cv2.cvtColor(rgb_frame, cv2.COLOR_GRAY2RGB)

    results = face_mesh.process(rgb_frame)
    if results.multi_face_landmarks:
        for face_landmarks in results.multi_face_landmarks:
            # Get right and left eye crops and bounding boxes
            right_eye_landmarks = [face_landmarks.landmark[i] for i in [33, 27, 23, 133]]
            left_eye_landmarks = [face_landmarks.landmark[i] for i in [362, 258, 253, 263]]

            right_eye_crop, right_eye_bbox = crop_eye(rgb_frame, right_eye_landmarks)
            left_eye_crop, left_eye_bbox = crop_eye(rgb_frame, left_eye_landmarks)

            # Display eye crops
            # Draw bounding boxes on the original frame
            if right_eye_crop is not None and 0 not in right_eye_crop.shape:
                cv2.imshow("Right Eye", right_eye_crop)
                cv2.rectangle(rgb_frame,
                              (right_eye_bbox[0], right_eye_bbox[1]),
                              (right_eye_bbox[2], right_eye_bbox[3]),
                              (0, 255, 0), 2)
            if left_eye_crop is not None and 0 not in right_eye_crop.shape:
                cv2.imshow("Left Eye", left_eye_crop)
                cv2.rectangle(rgb_frame,
                              (left_eye_bbox[0], left_eye_bbox[1]),
                              (left_eye_bbox[2], left_eye_bbox[3]),
                              (0, 255, 0), 2)


    cv2.imshow("Original Frame", rgb_frame)

    end_time = time.time()
    processing_time = end_time - start_time
    print(f"Processing Time: {processing_time:.4f} seconds")

    if cv2.waitKey(1) & 0xFF == ord('q'):
        break

cap.release()
cv2.destroyAllWindows()
