from src.common.config import Conf
import threading
import numpy as np
import cv2
import logging

from src.eyetracker.video_sources.base_usb_camera import  BaseUsbCamera
from src.eyetracker.MvImport.MvCameraControl_class import *

def get_str(obj):
    res = ""
    for per in obj:
        if per == 0:
            break
        res += chr(per)
    return res
    
class UsbCameraLeo(BaseUsbCamera):
    def __init__(self, conf:Conf):
        # TODO exceptions
        super().__init__(conf)
        deviceList = MV_CC_DEVICE_INFO_LIST()
        ret = MvCamera.MV_CC_EnumDevices(MV_USB_DEVICE, deviceList)
        if ret != 0:
            print("Enum devices fail! ret[0x%x]" % ret)
            sys.exit()

        if deviceList.nDeviceNum == 0:
            print("No devices found")
            sys.exit()
        logging.info("%d camera found" % deviceList.nDeviceNum)

        self.frame_index = 0
        self.timestamps = []
        self.cam = MvCamera()
        stDeviceList = cast(deviceList.pDeviceInfo[0], POINTER(MV_CC_DEVICE_INFO)).contents
        self.camera_info['type'] = 'Usb Camera'
        self.camera_info['manufacturer'] = get_str(stDeviceList.SpecialInfo.stUsb3VInfo.chManufacturerName)
        self.camera_info['model'] = get_str(stDeviceList.SpecialInfo.stUsb3VInfo.chModelName)
        self.camera_info['serial'] = get_str(stDeviceList.SpecialInfo.stUsb3VInfo.chSerialNumber)
        
        logging.info(f"{self.camera_info['manufacturer']} {self.camera_info['model']} {self.camera_info['serial']} camera detected")
        
        # ch:选择设备并创建句柄 | en:Select device and create handle
        ret = self.cam.MV_CC_CreateHandle(stDeviceList)
        if ret != 0:
            print("create handle fail! ret[0x%x]" % ret)
            sys.exit()

        # ch:打开设备 | en:Open device
        ret = self.cam.MV_CC_OpenDevice(MV_ACCESS_Exclusive, 0)
        if ret != 0:
            print("open device fail! ret[0x%x]" % ret)
            sys.exit()

        val = c_ulong(0)
        ret = self.cam.MV_CC_GetIntValue('Width', val)
        # print(f'{res=} {val=}')
        # print(type(val))
        if val.value > conf['usb_camera_config']['Width']:
            ret = self.cam.MV_CC_SetEnumValueByString('BinningHorizontal', 'BinningHorizontal2')
            # print(f"{ret=}")
        # config.USB_CAMERA_CONFIG['AcquisitionFrameRate']= float(self.fps)
        self.set_values(conf['usb_camera_config'])

        ret = self.cam.MV_CC_StartGrabbing()
        if ret != 0:
            print("start grabbing fail! ret[0x%x]" % ret)
            sys.exit()
        
    def set_values(self, values: dict):
        for k, v in values.items():
            func = None
            if isinstance(v, str):
                func = self.cam.MV_CC_SetEnumValueByString
            elif isinstance(v, bool):
                func = self.cam.MV_CC_SetBoolValue
                # v = 1 if v else 0
            elif isinstance(v, int):
                func = self.cam.MV_CC_SetIntValue
            elif isinstance(v, float):
                func = self.cam.MV_CC_SetFloatValue
            if func is None:
                continue
            ret = func(k, v)
            if ret != 0:
                print(f'Can\'t set {k}={v}')
                # sys.exit()

    def do_get_frame(self):
        stOutFrame = MV_FRAME_OUT()
        memset(byref(stOutFrame), 0, sizeof(stOutFrame))
        img = None
        ret = self.cam.MV_CC_GetImageBuffer(stOutFrame, 1000)
        if stOutFrame.pBufAddr is not None and ret == 0:
            # low = stOutFrame.stFrameInfo.nDevTimeStampLow
            # high = stOutFrame.stFrameInfo.nDevTimeStampHigh
            # host = stOutFrame.stFrameInfo.nHostTimeStamp
            # print (f"{(high << 32) + low:_} {high} {low:_} {host:_}")
            # print(stOutFrame.stFrameInfo.nDevTimeStampHigh, stOutFrame.stFrameInfo.nDevTimeStampLow, stOutFrame.stFrameInfo.nHostTimeStamp)
            # if self.frame_index < 10000: # and self.frame_index % 25 == 1:
            #     self.timestamps.append((high << 32) + low)
            w, h = stOutFrame.stFrameInfo.nWidth, stOutFrame.stFrameInfo.nHeight
            # print("get one frame: Width[%d], Height[%d], nFrameNum[%d]"
            #       % (
            #       stOutFrame.stFrameInfo.nWidth, stOutFrame.stFrameInfo.nHeight, stOutFrame.stFrameInfo.nFrameNum))
            buf_cache = (c_ubyte * stOutFrame.stFrameInfo.nFrameLen)()
            cdll.msvcrt.memcpy(byref(buf_cache), stOutFrame.pBufAddr, stOutFrame.stFrameInfo.nFrameLen)
            img = np.frombuffer(buf_cache, dtype=np.uint8).reshape((h, w))
            nRet = self.cam.MV_CC_FreeImageBuffer(stOutFrame)

        # self.frame_index += 1
        # if self.frame_index % 100 == 0:
        #     self.set_values({"Gain": 3 + random()*3, "ExposureTime": float(randint(1200, 1800))})
        # if self.frame_index == 1000:
        #     print(self.chrono1.print_ms(), self.chrono2.print_ms())
        # #     with open('times.csv', 'w') as fp:
        #         for i in range(1, len(self.timestamps), 1):
        #             fp.write(str(self.timestamps[i] - self.timestamps[i-1]) + '\n')
        return img, None

    def set_gain(self, gain=None):
        return self._set_gain(gain, log=True)

    def release(self):
        # TODO: implement
        pass


g_bExit = False


def work_thread(cam, pData):
    stOutFrame = MV_FRAME_OUT()
    memset(byref(stOutFrame), 0, sizeof(stOutFrame))
    # while True:
    for i in range(1):
        ret = cam.MV_CC_GetImageBuffer(stOutFrame, 1000)
        if stOutFrame.pBufAddr is not None and ret == 0:
            w, h = stOutFrame.stFrameInfo.nWidth, stOutFrame.stFrameInfo.nHeight
            print("get one frame: Width[%d], Height[%d], nFrameNum[%d]"
                  % (stOutFrame.stFrameInfo.nWidth, stOutFrame.stFrameInfo.nHeight, stOutFrame.stFrameInfo.nFrameNum))
            buf_cache = (c_ubyte * stOutFrame.stFrameInfo.nFrameLen)()
            cdll.msvcrt.memcpy(byref(buf_cache), stOutFrame.pBufAddr, stOutFrame.stFrameInfo.nFrameLen)
            img = np.frombuffer(buf_cache, dtype=np.uint8).reshape((h, w))
            cv2.imshow('111', img)
            cv2.waitKey(0)
            nRet = cam.MV_CC_FreeImageBuffer(stOutFrame)

        else:
            print("no data[0x%x]" % ret)
        if g_bExit:
            break


def test():
    deviceList = MV_CC_DEVICE_INFO_LIST()
    ret = MvCamera.MV_CC_EnumDevices(MV_USB_DEVICE, deviceList)
    if ret != 0:
        print("Enum devices fail! ret[0x%x]" % ret)
        sys.exit()

    if deviceList.nDeviceNum == 0:
        print("No devices found")
        sys.exit()
    print("%d devices found" % deviceList.nDeviceNum)

    devList = []
    for i in range(0, deviceList.nDeviceNum):
        mvcc_dev_info = cast(deviceList.pDeviceInfo[i], POINTER(MV_CC_DEVICE_INFO)).contents
        if mvcc_dev_info.nTLayerType == MV_USB_DEVICE:
            print("\nu3v device: [%d]" % i)
            chUserDefinedName = ""
            for per in mvcc_dev_info.SpecialInfo.stUsb3VInfo.chUserDefinedName:
                if per == 0:
                    break
                chUserDefinedName = chUserDefinedName + chr(per)
            print("device user define name: %s" % chUserDefinedName)

            chModelName = ""
            for per in mvcc_dev_info.SpecialInfo.stUsb3VInfo.chModelName:
                if 0 == per:
                    break
                chModelName = chModelName + chr(per)
            print("device model name: %s" % chModelName)

            strSerialNumber = ""
            for per in mvcc_dev_info.SpecialInfo.stUsb3VInfo.chSerialNumber:
                if per == 0:
                    break
                strSerialNumber = strSerialNumber + chr(per)
            print("user serial number: %s" % strSerialNumber)
            # devList.append("[" + str(i) + "]USB: " + chUserDefinedName + " " + chModelName
            #                + "(" + str(strSerialNumber) + ")")

    # stDevInfo = MV_CC_DEVICE_INFO()
    # stUsbDev = MV_USB3_DEVICE_INFO()
    #
    # stDevInfo.nTLayerType = MV_USB_DEVICE
    # stDevInfo.SpecialInfo.stUsb3VInfo = stUsbDev

    # ch:创建相机实例 | en:Creat Camera Object
    cam = MvCamera()
    stDeviceList = cast(deviceList.pDeviceInfo[0], POINTER(MV_CC_DEVICE_INFO)).contents
    # ch:选择设备并创建句柄 | en:Select device and create handle
    ret = cam.MV_CC_CreateHandle(stDeviceList)
    if ret != 0:
        print("create handle fail! ret[0x%x]" % ret)
        sys.exit()

    # ch:打开设备 | en:Open device
    ret = cam.MV_CC_OpenDevice(MV_ACCESS_Exclusive, 0)
    if ret != 0:
        print("open device fail! ret[0x%x]" % ret)
        sys.exit()

    # set_values(cam, val_dict)
    # print ("start export the camera properties to the file")
    # print ("wait......")
    #
    # #ch:将相机属性导出到文件中 | en:Export the camera properties to the file
    # ret = cam.MV_CC_FeatureSave("FeatureFile.ini")
    # if MV_OK != ret:
    #     print ("save feature fail! ret [0x%x]" % ret)
    # print ("finish export the camera properties to the file")

    # ch:开始取流 | en:Start grab image
    ret = cam.MV_CC_StartGrabbing()
    if ret != 0:
        print("start grabbing fail! ret[0x%x]" % ret)
        sys.exit()
    try:
        hThreadHandle = threading.Thread(target=work_thread, args=(cam, None))
        hThreadHandle.start()
    except:
        print("error: unable to start thread")

    print("press a key to stop grabbing.")
    # msvcrt.getch()

    global g_bExit
    g_bExit = True
    hThreadHandle.join()

    # ch:停止取流 | en:Stop grab image
    ret = cam.MV_CC_StopGrabbing()
    if ret != 0:
        print("stop grabbing fail! ret[0x%x]" % ret)
        sys.exit()

    # ch:关闭设备 | Close device
    ret = cam.MV_CC_CloseDevice()
    if ret != 0:
        print("close deivce fail! ret[0x%x]" % ret)
        sys.exit()

    # ch:销毁句柄 | Destroy handle
    ret = cam.MV_CC_DestroyHandle()
    if ret != 0:
        print("destroy handle fail! ret[0x%x]" % ret)
        sys.exit()


if __name__ == '__main__':
    test()
