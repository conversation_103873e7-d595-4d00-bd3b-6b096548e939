<?xml version="1.0"?>
<net name="torch_jit" version="11">
	<layers>
		<layer id="0" name="input" type="Parameter" version="opset1">
			<data shape="2,1,64,64" element_type="f32" />
			<output>
				<port id="0" precision="FP32" names="input">
					<dim>2</dim>
					<dim>1</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="1" name="onnx::Conv_341" type="Const" version="opset1">
			<data element_type="f32" shape="8, 1, 3, 3" offset="0" size="288" />
			<output>
				<port id="0" precision="FP32" names="onnx::Conv_341">
					<dim>8</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="2" name="/inc/double_conv/double_conv.0/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>1</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>8</dim>
					<dim>1</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>2</dim>
					<dim>8</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="3" name="Reshape_54" type="Const" version="opset1">
			<data element_type="f32" shape="1, 8, 1, 1" offset="288" size="32" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="4" name="/inc/double_conv/double_conv.0/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>8</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/inc/double_conv/double_conv.0/Conv_output_0">
					<dim>2</dim>
					<dim>8</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="5" name="/inc/double_conv/double_conv.2/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>8</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/inc/double_conv/double_conv.2/Relu_output_0">
					<dim>2</dim>
					<dim>8</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="6" name="onnx::Conv_344" type="Const" version="opset1">
			<data element_type="f32" shape="8, 8, 3, 3" offset="320" size="2304" />
			<output>
				<port id="0" precision="FP32" names="onnx::Conv_344">
					<dim>8</dim>
					<dim>8</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="7" name="/inc/double_conv/double_conv.3/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>8</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>8</dim>
					<dim>8</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>2</dim>
					<dim>8</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="8" name="Reshape_71" type="Const" version="opset1">
			<data element_type="f32" shape="1, 8, 1, 1" offset="2624" size="32" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="9" name="/inc/double_conv/double_conv.3/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>8</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/inc/double_conv/double_conv.3/Conv_output_0">
					<dim>2</dim>
					<dim>8</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="10" name="/inc/double_conv/double_conv.5/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>8</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/inc/double_conv/double_conv.5/Relu_output_0">
					<dim>2</dim>
					<dim>8</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="11" name="/down1/maxpool_conv/maxpool_conv.0/MaxPool" type="MaxPool" version="opset8">
			<data strides="2, 2" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" kernel="2, 2" rounding_type="floor" auto_pad="explicit" index_element_type="i64" axis="0" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>8</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/down1/maxpool_conv/maxpool_conv.0/MaxPool_output_0">
					<dim>2</dim>
					<dim>8</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="2" precision="I64">
					<dim>2</dim>
					<dim>8</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="12" name="onnx::Conv_347" type="Const" version="opset1">
			<data element_type="f32" shape="16, 8, 3, 3" offset="2656" size="4608" />
			<output>
				<port id="0" precision="FP32" names="onnx::Conv_347">
					<dim>16</dim>
					<dim>8</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="13" name="/down1/maxpool_conv/maxpool_conv.1/double_conv/double_conv.0/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>8</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>16</dim>
					<dim>8</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>2</dim>
					<dim>16</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="14" name="Reshape_89" type="Const" version="opset1">
			<data element_type="f32" shape="1, 16, 1, 1" offset="7264" size="64" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="15" name="/down1/maxpool_conv/maxpool_conv.1/double_conv/double_conv.0/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>16</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/down1/maxpool_conv/maxpool_conv.1/double_conv/double_conv.0/Conv_output_0">
					<dim>2</dim>
					<dim>16</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="16" name="/down1/maxpool_conv/maxpool_conv.1/double_conv/double_conv.2/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>16</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/down1/maxpool_conv/maxpool_conv.1/double_conv/double_conv.2/Relu_output_0">
					<dim>2</dim>
					<dim>16</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="17" name="onnx::Conv_350" type="Const" version="opset1">
			<data element_type="f32" shape="16, 16, 3, 3" offset="7328" size="9216" />
			<output>
				<port id="0" precision="FP32" names="onnx::Conv_350">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="18" name="/down1/maxpool_conv/maxpool_conv.1/double_conv/double_conv.3/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>16</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>2</dim>
					<dim>16</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="19" name="Reshape_106" type="Const" version="opset1">
			<data element_type="f32" shape="1, 16, 1, 1" offset="16544" size="64" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="20" name="/down1/maxpool_conv/maxpool_conv.1/double_conv/double_conv.3/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>16</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/down1/maxpool_conv/maxpool_conv.1/double_conv/double_conv.3/Conv_output_0">
					<dim>2</dim>
					<dim>16</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="21" name="/down1/maxpool_conv/maxpool_conv.1/double_conv/double_conv.5/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>16</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/down1/maxpool_conv/maxpool_conv.1/double_conv/double_conv.5/Relu_output_0">
					<dim>2</dim>
					<dim>16</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="22" name="/down2/maxpool_conv/maxpool_conv.0/MaxPool" type="MaxPool" version="opset8">
			<data strides="2, 2" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" kernel="2, 2" rounding_type="floor" auto_pad="explicit" index_element_type="i64" axis="0" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>16</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/down2/maxpool_conv/maxpool_conv.0/MaxPool_output_0">
					<dim>2</dim>
					<dim>16</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="2" precision="I64">
					<dim>2</dim>
					<dim>16</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="23" name="onnx::Conv_353" type="Const" version="opset1">
			<data element_type="f32" shape="32, 16, 3, 3" offset="16608" size="18432" />
			<output>
				<port id="0" precision="FP32" names="onnx::Conv_353">
					<dim>32</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="24" name="/down2/maxpool_conv/maxpool_conv.1/double_conv/double_conv.0/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>16</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>2</dim>
					<dim>32</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="25" name="Reshape_124" type="Const" version="opset1">
			<data element_type="f32" shape="1, 32, 1, 1" offset="35040" size="128" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="26" name="/down2/maxpool_conv/maxpool_conv.1/double_conv/double_conv.0/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>32</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/down2/maxpool_conv/maxpool_conv.1/double_conv/double_conv.0/Conv_output_0">
					<dim>2</dim>
					<dim>32</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="27" name="/down2/maxpool_conv/maxpool_conv.1/double_conv/double_conv.2/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>32</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/down2/maxpool_conv/maxpool_conv.1/double_conv/double_conv.2/Relu_output_0">
					<dim>2</dim>
					<dim>32</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="28" name="onnx::Conv_356" type="Const" version="opset1">
			<data element_type="f32" shape="32, 32, 3, 3" offset="35168" size="36864" />
			<output>
				<port id="0" precision="FP32" names="onnx::Conv_356">
					<dim>32</dim>
					<dim>32</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="29" name="/down2/maxpool_conv/maxpool_conv.1/double_conv/double_conv.3/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>32</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>32</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>2</dim>
					<dim>32</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="30" name="Reshape_141" type="Const" version="opset1">
			<data element_type="f32" shape="1, 32, 1, 1" offset="72032" size="128" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="31" name="/down2/maxpool_conv/maxpool_conv.1/double_conv/double_conv.3/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>32</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/down2/maxpool_conv/maxpool_conv.1/double_conv/double_conv.3/Conv_output_0">
					<dim>2</dim>
					<dim>32</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="32" name="/down2/maxpool_conv/maxpool_conv.1/double_conv/double_conv.5/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>32</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/down2/maxpool_conv/maxpool_conv.1/double_conv/double_conv.5/Relu_output_0">
					<dim>2</dim>
					<dim>32</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="33" name="Constant_193" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="72160" size="16" />
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="34" name="/Flatten" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>1</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="I64">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/Flatten_output_0">
					<dim>2</dim>
					<dim>4096</dim>
				</port>
			</output>
		</layer>
		<layer id="35" name="fc1.weight" type="Const" version="opset1">
			<data element_type="f32" shape="128, 4096" offset="72176" size="2097152" />
			<output>
				<port id="0" precision="FP32" names="fc1.weight">
					<dim>128</dim>
					<dim>4096</dim>
				</port>
			</output>
		</layer>
		<layer id="36" name="/fc1/Gemm/WithoutBiases" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>4096</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>4096</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>2</dim>
					<dim>128</dim>
				</port>
			</output>
		</layer>
		<layer id="37" name="Constant_7585" type="Const" version="opset1">
			<data element_type="f32" shape="1, 128" offset="2169328" size="512" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
				</port>
			</output>
		</layer>
		<layer id="38" name="/fc1/Gemm" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>128</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/fc1/Gemm_output_0">
					<dim>2</dim>
					<dim>128</dim>
				</port>
			</output>
		</layer>
		<layer id="39" name="/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>128</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/Relu_output_0">
					<dim>2</dim>
					<dim>128</dim>
				</port>
			</output>
		</layer>
		<layer id="40" name="fc2.weight" type="Const" version="opset1">
			<data element_type="f32" shape="4096, 128" offset="2169840" size="2097152" />
			<output>
				<port id="0" precision="FP32" names="fc2.weight">
					<dim>4096</dim>
					<dim>128</dim>
				</port>
			</output>
		</layer>
		<layer id="41" name="/fc2/Gemm/WithoutBiases" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>128</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>4096</dim>
					<dim>128</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>2</dim>
					<dim>4096</dim>
				</port>
			</output>
		</layer>
		<layer id="42" name="Constant_7586" type="Const" version="opset1">
			<data element_type="f32" shape="1, 4096" offset="4266992" size="16384" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>4096</dim>
				</port>
			</output>
		</layer>
		<layer id="43" name="/fc2/Gemm" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>4096</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>4096</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/fc2/Gemm_output_0">
					<dim>2</dim>
					<dim>4096</dim>
				</port>
			</output>
		</layer>
		<layer id="44" name="/Relu_1" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>4096</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/Relu_1_output_0">
					<dim>2</dim>
					<dim>4096</dim>
				</port>
			</output>
		</layer>
		<layer id="45" name="/Constant_3" type="Const" version="opset1">
			<data element_type="i64" shape="1" offset="4283376" size="8" />
			<output>
				<port id="0" precision="I64" names="/Constant_3_output_0">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="46" name="/down3/maxpool_conv/maxpool_conv.0/MaxPool" type="MaxPool" version="opset8">
			<data strides="2, 2" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" kernel="2, 2" rounding_type="floor" auto_pad="explicit" index_element_type="i64" axis="0" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>32</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/down3/maxpool_conv/maxpool_conv.0/MaxPool_output_0">
					<dim>2</dim>
					<dim>32</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
				<port id="2" precision="I64">
					<dim>2</dim>
					<dim>32</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
			</output>
		</layer>
		<layer id="47" name="onnx::Conv_359" type="Const" version="opset1">
			<data element_type="f32" shape="64, 32, 3, 3" offset="4283384" size="73728" />
			<output>
				<port id="0" precision="FP32" names="onnx::Conv_359">
					<dim>64</dim>
					<dim>32</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="48" name="/down3/maxpool_conv/maxpool_conv.1/double_conv/double_conv.0/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>32</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>32</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>2</dim>
					<dim>64</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
			</output>
		</layer>
		<layer id="49" name="Reshape_159" type="Const" version="opset1">
			<data element_type="f32" shape="1, 64, 1, 1" offset="4357112" size="256" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="50" name="/down3/maxpool_conv/maxpool_conv.1/double_conv/double_conv.0/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>64</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/down3/maxpool_conv/maxpool_conv.1/double_conv/double_conv.0/Conv_output_0">
					<dim>2</dim>
					<dim>64</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
			</output>
		</layer>
		<layer id="51" name="/down3/maxpool_conv/maxpool_conv.1/double_conv/double_conv.2/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>64</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/down3/maxpool_conv/maxpool_conv.1/double_conv/double_conv.2/Relu_output_0">
					<dim>2</dim>
					<dim>64</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
			</output>
		</layer>
		<layer id="52" name="onnx::Conv_362" type="Const" version="opset1">
			<data element_type="f32" shape="64, 64, 3, 3" offset="4357368" size="147456" />
			<output>
				<port id="0" precision="FP32" names="onnx::Conv_362">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="53" name="/down3/maxpool_conv/maxpool_conv.1/double_conv/double_conv.3/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>64</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>2</dim>
					<dim>64</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
			</output>
		</layer>
		<layer id="54" name="Reshape_176" type="Const" version="opset1">
			<data element_type="f32" shape="1, 64, 1, 1" offset="4504824" size="256" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="55" name="/down3/maxpool_conv/maxpool_conv.1/double_conv/double_conv.3/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>64</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/down3/maxpool_conv/maxpool_conv.1/double_conv/double_conv.3/Conv_output_0">
					<dim>2</dim>
					<dim>64</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
			</output>
		</layer>
		<layer id="56" name="/down3/maxpool_conv/maxpool_conv.1/double_conv/double_conv.5/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>64</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/down3/maxpool_conv/maxpool_conv.1/double_conv/double_conv.5/Relu_output_0">
					<dim>2</dim>
					<dim>64</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
			</output>
		</layer>
		<layer id="57" name="/Shape" type="ShapeOf" version="opset3">
			<data output_type="i64" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>64</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="I64" names="/Shape_1_output_0,/Shape_2_output_0,/Shape_output_0">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="58" name="Constant_5501" type="Const" version="opset1">
			<data element_type="i64" shape="3" offset="4505080" size="24" />
			<output>
				<port id="0" precision="I64">
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="59" name="Constant_5502" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="4505104" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="60" name="Gather_5503" type="Gather" version="opset8">
			<data batch_dims="0" />
			<input>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
				<port id="1" precision="I64">
					<dim>3</dim>
				</port>
				<port id="2" precision="I64" />
			</input>
			<output>
				<port id="3" precision="I64">
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="61" name="/Concat" type="Concat" version="opset1">
			<data axis="0" />
			<input>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
				<port id="1" precision="I64">
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="I64" names="/Concat_output_0">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="62" name="/Reshape" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>4096</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/Reshape_output_0">
					<dim>2</dim>
					<dim>64</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
			</output>
		</layer>
		<layer id="63" name="up1.up.weight" type="Const" version="opset1">
			<data element_type="f32" shape="64, 32, 2, 2" offset="4505112" size="32768" />
			<output>
				<port id="0" precision="FP32" names="up1.up.weight">
					<dim>64</dim>
					<dim>32</dim>
					<dim>2</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="64" name="ConvolutionBackpropData_221" type="ConvolutionBackpropData" version="opset1">
			<data strides="2, 2" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" output_padding="0, 0" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>64</dim>
					<dim>8</dim>
					<dim>8</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>32</dim>
					<dim>2</dim>
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>2</dim>
					<dim>32</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="65" name="Reshape_223" type="Const" version="opset1">
			<data element_type="f32" shape="1, 32, 1, 1" offset="4537880" size="128" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="66" name="/up1/up/ConvTranspose" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>32</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/up1/up/ConvTranspose_output_0">
					<dim>2</dim>
					<dim>32</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="67" name="/up1/Shape" type="ShapeOf" version="opset3">
			<data output_type="i64" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>32</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="I64" names="/up1/Shape_2_output_0,/up1/Shape_output_0">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="68" name="/up1/Constant_2" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="4538008" size="8" />
			<output>
				<port id="0" precision="I64" names="/up1/Constant_2_output_0" />
			</output>
		</layer>
		<layer id="69" name="Constant_236" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="4505104" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="70" name="/up1/Gather_2" type="Gather" version="opset8">
			<data batch_dims="0" />
			<input>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
				<port id="1" precision="I64" />
				<port id="2" precision="I64" />
			</input>
			<output>
				<port id="3" precision="I64" names="/up1/Gather_2_output_0" />
			</output>
		</layer>
		<layer id="71" name="/up1/Shape_1" type="ShapeOf" version="opset3">
			<data output_type="i64" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>32</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="I64" names="/up1/Shape_1_output_0,/up1/Shape_3_output_0">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="72" name="/up1/Constant_3" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="4538008" size="8" />
			<output>
				<port id="0" precision="I64" names="/up1/Constant_3_output_0" />
			</output>
		</layer>
		<layer id="73" name="Constant_240" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="4505104" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="74" name="/up1/Gather_3" type="Gather" version="opset8">
			<data batch_dims="0" />
			<input>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
				<port id="1" precision="I64" />
				<port id="2" precision="I64" />
			</input>
			<output>
				<port id="3" precision="I64" names="/up1/Gather_3_output_0" />
			</output>
		</layer>
		<layer id="75" name="/up1/Sub_1" type="Subtract" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="I64" />
				<port id="1" precision="I64" />
			</input>
			<output>
				<port id="2" precision="I64" names="/up1/Sub_1_output_0" />
			</output>
		</layer>
		<layer id="76" name="/up1/Constant_4" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="4538016" size="8" />
			<output>
				<port id="0" precision="I64" names="/up1/Constant_4_output_0" />
			</output>
		</layer>
		<layer id="77" name="/up1/Div" type="Divide" version="opset1">
			<data auto_broadcast="numpy" m_pythondiv="true" />
			<input>
				<port id="0" precision="I64" />
				<port id="1" precision="I64" />
			</input>
			<output>
				<port id="2" precision="I64" names="/up1/Cast_1_output_0,/up1/Cast_output_0,/up1/Div_output_0" />
			</output>
		</layer>
		<layer id="78" name="Constant_262" type="Const" version="opset1">
			<data element_type="i64" shape="1" offset="4505104" size="8" />
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="79" name="/up1/Unsqueeze_4" type="Unsqueeze" version="opset1">
			<input>
				<port id="0" precision="I64" />
				<port id="1" precision="I64">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="I64" names="/up1/Unsqueeze_4_output_0">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="80" name="/up1/Sub_2" type="Subtract" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="I64" />
				<port id="1" precision="I64" />
			</input>
			<output>
				<port id="2" precision="I64" names="/up1/Sub_2_output_0" />
			</output>
		</layer>
		<layer id="81" name="Constant_264" type="Const" version="opset1">
			<data element_type="i64" shape="1" offset="4505104" size="8" />
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="82" name="/up1/Unsqueeze_5" type="Unsqueeze" version="opset1">
			<input>
				<port id="0" precision="I64" />
				<port id="1" precision="I64">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="I64" names="/up1/Unsqueeze_5_output_0">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="83" name="/up1/Constant" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="4538016" size="8" />
			<output>
				<port id="0" precision="I64" names="/up1/Constant_output_0" />
			</output>
		</layer>
		<layer id="84" name="Constant_227" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="4505104" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="85" name="/up1/Gather" type="Gather" version="opset8">
			<data batch_dims="0" />
			<input>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
				<port id="1" precision="I64" />
				<port id="2" precision="I64" />
			</input>
			<output>
				<port id="3" precision="I64" names="/up1/Gather_output_0" />
			</output>
		</layer>
		<layer id="86" name="/up1/Constant_1" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="4538016" size="8" />
			<output>
				<port id="0" precision="I64" names="/up1/Constant_1_output_0" />
			</output>
		</layer>
		<layer id="87" name="Constant_231" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="4505104" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="88" name="/up1/Gather_1" type="Gather" version="opset8">
			<data batch_dims="0" />
			<input>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
				<port id="1" precision="I64" />
				<port id="2" precision="I64" />
			</input>
			<output>
				<port id="3" precision="I64" names="/up1/Gather_1_output_0" />
			</output>
		</layer>
		<layer id="89" name="/up1/Sub" type="Subtract" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="I64" />
				<port id="1" precision="I64" />
			</input>
			<output>
				<port id="2" precision="I64" names="/up1/Sub_output_0" />
			</output>
		</layer>
		<layer id="90" name="/up1/Constant_5" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="4538016" size="8" />
			<output>
				<port id="0" precision="I64" names="/up1/Constant_5_output_0" />
			</output>
		</layer>
		<layer id="91" name="/up1/Div_1" type="Divide" version="opset1">
			<data auto_broadcast="numpy" m_pythondiv="true" />
			<input>
				<port id="0" precision="I64" />
				<port id="1" precision="I64" />
			</input>
			<output>
				<port id="2" precision="I64" names="/up1/Cast_2_output_0,/up1/Cast_3_output_0,/up1/Div_1_output_0" />
			</output>
		</layer>
		<layer id="92" name="Constant_266" type="Const" version="opset1">
			<data element_type="i64" shape="1" offset="4505104" size="8" />
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="93" name="/up1/Unsqueeze_6" type="Unsqueeze" version="opset1">
			<input>
				<port id="0" precision="I64" />
				<port id="1" precision="I64">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="I64" names="/up1/Unsqueeze_6_output_0">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="94" name="/up1/Sub_3" type="Subtract" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="I64" />
				<port id="1" precision="I64" />
			</input>
			<output>
				<port id="2" precision="I64" names="/up1/Sub_3_output_0" />
			</output>
		</layer>
		<layer id="95" name="Constant_268" type="Const" version="opset1">
			<data element_type="i64" shape="1" offset="4505104" size="8" />
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="96" name="/up1/Unsqueeze_7" type="Unsqueeze" version="opset1">
			<input>
				<port id="0" precision="I64" />
				<port id="1" precision="I64">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="I64" names="/up1/Unsqueeze_7_output_0">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="97" name="/up1/Concat_1" type="Concat" version="opset1">
			<data axis="0" />
			<input>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
				<port id="1" precision="I64">
					<dim>1</dim>
				</port>
				<port id="2" precision="I64">
					<dim>1</dim>
				</port>
				<port id="3" precision="I64">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="4" precision="I64" names="/up1/Cast_4_output_0,/up1/Concat_1_output_0">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="98" name="/up1/ConstantOfShape" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="4538024" size="32" />
			<output>
				<port id="0" precision="I64" names="/up1/ConstantOfShape_output_0">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="99" name="/up1/Concat_2" type="Concat" version="opset1">
			<data axis="0" />
			<input>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="I64" names="/up1/Concat_2_output_0">
					<dim>8</dim>
				</port>
			</output>
		</layer>
		<layer id="100" name="/up1/Constant_8" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="4538056" size="16" />
			<output>
				<port id="0" precision="I64" names="/up1/Constant_8_output_0">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="101" name="/up1/Reshape" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="I64">
					<dim>8</dim>
				</port>
				<port id="1" precision="I64">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="I64" names="/up1/Reshape_output_0">
					<dim>4</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="102" name="/up1/Constant_10" type="Const" version="opset1">
			<data element_type="i64" shape="1" offset="4283376" size="8" />
			<output>
				<port id="0" precision="I64" names="/up1/Constant_10_output_0">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="103" name="/up1/Constant_11" type="Const" version="opset1">
			<data element_type="i64" shape="1" offset="4538072" size="8" />
			<output>
				<port id="0" precision="I64" names="/up1/Constant_11_output_0">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="104" name="/up1/Constant_12" type="Const" version="opset1">
			<data element_type="i64" shape="1" offset="4283376" size="8" />
			<output>
				<port id="0" precision="I64" names="/up1/Constant_12_output_0">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="105" name="/up1/Slice" type="StridedSlice" version="opset1">
			<data begin_mask="0" end_mask="0" new_axis_mask="" shrink_axis_mask="" ellipsis_mask="" />
			<input>
				<port id="0" precision="I64">
					<dim>4</dim>
					<dim>2</dim>
				</port>
				<port id="1" precision="I64">
					<dim>1</dim>
				</port>
				<port id="2" precision="I64">
					<dim>1</dim>
				</port>
				<port id="3" precision="I64">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="4" precision="I64" names="/up1/Slice_output_0">
					<dim>4</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="106" name="Constant_292" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="4538080" size="16" />
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="107" name="/up1/Transpose" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="I64">
					<dim>4</dim>
					<dim>2</dim>
				</port>
				<port id="1" precision="I64">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="I64" names="/up1/Transpose_output_0">
					<dim>2</dim>
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="108" name="Constant_1369" type="Const" version="opset1">
			<data element_type="i64" shape="1" offset="4283376" size="8" />
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="109" name="/up1/Reshape_1" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="I64">
					<dim>2</dim>
					<dim>4</dim>
				</port>
				<port id="1" precision="I64">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="I64" names="/up1/Cast_5_output_0,/up1/Reshape_1_output_0">
					<dim>8</dim>
				</port>
			</output>
		</layer>
		<layer id="110" name="Constant_299" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="4505104" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="111" name="Split_300" type="Split" version="opset1">
			<data num_splits="2" />
			<input>
				<port id="0" precision="I64">
					<dim>8</dim>
				</port>
				<port id="1" precision="I64" />
			</input>
			<output>
				<port id="2" precision="I64">
					<dim>4</dim>
				</port>
				<port id="3" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="112" name="Constant_298" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="4538096" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="113" name="/up1/Pad" type="Pad" version="opset12">
			<data pad_mode="constant" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>32</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
				<port id="2" precision="I64">
					<dim>4</dim>
				</port>
				<port id="3" precision="FP32" />
			</input>
			<output>
				<port id="4" precision="FP32" names="/up1/Pad_output_0">
					<dim>2</dim>
					<dim>32</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="114" name="/up1/Concat_3" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>32</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>2</dim>
					<dim>32</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/up1/Concat_3_output_0">
					<dim>2</dim>
					<dim>64</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="115" name="onnx::Conv_365" type="Const" version="opset1">
			<data element_type="f32" shape="32, 64, 3, 3" offset="4538100" size="73728" />
			<output>
				<port id="0" precision="FP32" names="onnx::Conv_365">
					<dim>32</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="116" name="/up1/conv/double_conv/double_conv.0/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>64</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>2</dim>
					<dim>32</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="117" name="Reshape_420" type="Const" version="opset1">
			<data element_type="f32" shape="1, 32, 1, 1" offset="4611828" size="128" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="118" name="/up1/conv/double_conv/double_conv.0/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>32</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/up1/conv/double_conv/double_conv.0/Conv_output_0">
					<dim>2</dim>
					<dim>32</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="119" name="/up1/conv/double_conv/double_conv.2/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>32</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/up1/conv/double_conv/double_conv.2/Relu_output_0">
					<dim>2</dim>
					<dim>32</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="120" name="onnx::Conv_368" type="Const" version="opset1">
			<data element_type="f32" shape="32, 32, 3, 3" offset="4611956" size="36864" />
			<output>
				<port id="0" precision="FP32" names="onnx::Conv_368">
					<dim>32</dim>
					<dim>32</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="121" name="/up1/conv/double_conv/double_conv.3/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>32</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>32</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>2</dim>
					<dim>32</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="122" name="Reshape_437" type="Const" version="opset1">
			<data element_type="f32" shape="1, 32, 1, 1" offset="4648820" size="128" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="123" name="/up1/conv/double_conv/double_conv.3/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>32</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/up1/conv/double_conv/double_conv.3/Conv_output_0">
					<dim>2</dim>
					<dim>32</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="124" name="/up1/conv/double_conv/double_conv.5/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>32</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/up1/conv/double_conv/double_conv.5/Relu_output_0">
					<dim>2</dim>
					<dim>32</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
			</output>
		</layer>
		<layer id="125" name="up2.up.weight" type="Const" version="opset1">
			<data element_type="f32" shape="32, 16, 2, 2" offset="4648948" size="8192" />
			<output>
				<port id="0" precision="FP32" names="up2.up.weight">
					<dim>32</dim>
					<dim>16</dim>
					<dim>2</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="126" name="ConvolutionBackpropData_442" type="ConvolutionBackpropData" version="opset1">
			<data strides="2, 2" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" output_padding="0, 0" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>32</dim>
					<dim>16</dim>
					<dim>16</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>16</dim>
					<dim>2</dim>
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>2</dim>
					<dim>16</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="127" name="Reshape_444" type="Const" version="opset1">
			<data element_type="f32" shape="1, 16, 1, 1" offset="4657140" size="64" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="128" name="/up2/up/ConvTranspose" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>16</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/up2/up/ConvTranspose_output_0">
					<dim>2</dim>
					<dim>16</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="129" name="/up2/Shape" type="ShapeOf" version="opset3">
			<data output_type="i64" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>16</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="I64" names="/up2/Shape_2_output_0,/up2/Shape_output_0">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="130" name="/up2/Constant_2" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="4538008" size="8" />
			<output>
				<port id="0" precision="I64" names="/up2/Constant_2_output_0" />
			</output>
		</layer>
		<layer id="131" name="Constant_457" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="4505104" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="132" name="/up2/Gather_2" type="Gather" version="opset8">
			<data batch_dims="0" />
			<input>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
				<port id="1" precision="I64" />
				<port id="2" precision="I64" />
			</input>
			<output>
				<port id="3" precision="I64" names="/up2/Gather_2_output_0" />
			</output>
		</layer>
		<layer id="133" name="/up2/Shape_1" type="ShapeOf" version="opset3">
			<data output_type="i64" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>16</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="I64" names="/up2/Shape_1_output_0,/up2/Shape_3_output_0">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="134" name="/up2/Constant_3" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="4538008" size="8" />
			<output>
				<port id="0" precision="I64" names="/up2/Constant_3_output_0" />
			</output>
		</layer>
		<layer id="135" name="Constant_461" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="4505104" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="136" name="/up2/Gather_3" type="Gather" version="opset8">
			<data batch_dims="0" />
			<input>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
				<port id="1" precision="I64" />
				<port id="2" precision="I64" />
			</input>
			<output>
				<port id="3" precision="I64" names="/up2/Gather_3_output_0" />
			</output>
		</layer>
		<layer id="137" name="/up2/Sub_1" type="Subtract" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="I64" />
				<port id="1" precision="I64" />
			</input>
			<output>
				<port id="2" precision="I64" names="/up2/Sub_1_output_0" />
			</output>
		</layer>
		<layer id="138" name="/up2/Constant_4" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="4538016" size="8" />
			<output>
				<port id="0" precision="I64" names="/up2/Constant_4_output_0" />
			</output>
		</layer>
		<layer id="139" name="/up2/Div" type="Divide" version="opset1">
			<data auto_broadcast="numpy" m_pythondiv="true" />
			<input>
				<port id="0" precision="I64" />
				<port id="1" precision="I64" />
			</input>
			<output>
				<port id="2" precision="I64" names="/up2/Cast_1_output_0,/up2/Cast_output_0,/up2/Div_output_0" />
			</output>
		</layer>
		<layer id="140" name="Constant_483" type="Const" version="opset1">
			<data element_type="i64" shape="1" offset="4505104" size="8" />
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="141" name="/up2/Unsqueeze_4" type="Unsqueeze" version="opset1">
			<input>
				<port id="0" precision="I64" />
				<port id="1" precision="I64">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="I64" names="/up2/Unsqueeze_4_output_0">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="142" name="/up2/Sub_2" type="Subtract" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="I64" />
				<port id="1" precision="I64" />
			</input>
			<output>
				<port id="2" precision="I64" names="/up2/Sub_2_output_0" />
			</output>
		</layer>
		<layer id="143" name="Constant_485" type="Const" version="opset1">
			<data element_type="i64" shape="1" offset="4505104" size="8" />
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="144" name="/up2/Unsqueeze_5" type="Unsqueeze" version="opset1">
			<input>
				<port id="0" precision="I64" />
				<port id="1" precision="I64">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="I64" names="/up2/Unsqueeze_5_output_0">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="145" name="/up2/Constant" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="4538016" size="8" />
			<output>
				<port id="0" precision="I64" names="/up2/Constant_output_0" />
			</output>
		</layer>
		<layer id="146" name="Constant_448" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="4505104" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="147" name="/up2/Gather" type="Gather" version="opset8">
			<data batch_dims="0" />
			<input>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
				<port id="1" precision="I64" />
				<port id="2" precision="I64" />
			</input>
			<output>
				<port id="3" precision="I64" names="/up2/Gather_output_0" />
			</output>
		</layer>
		<layer id="148" name="/up2/Constant_1" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="4538016" size="8" />
			<output>
				<port id="0" precision="I64" names="/up2/Constant_1_output_0" />
			</output>
		</layer>
		<layer id="149" name="Constant_452" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="4505104" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="150" name="/up2/Gather_1" type="Gather" version="opset8">
			<data batch_dims="0" />
			<input>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
				<port id="1" precision="I64" />
				<port id="2" precision="I64" />
			</input>
			<output>
				<port id="3" precision="I64" names="/up2/Gather_1_output_0" />
			</output>
		</layer>
		<layer id="151" name="/up2/Sub" type="Subtract" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="I64" />
				<port id="1" precision="I64" />
			</input>
			<output>
				<port id="2" precision="I64" names="/up2/Sub_output_0" />
			</output>
		</layer>
		<layer id="152" name="/up2/Constant_5" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="4538016" size="8" />
			<output>
				<port id="0" precision="I64" names="/up2/Constant_5_output_0" />
			</output>
		</layer>
		<layer id="153" name="/up2/Div_1" type="Divide" version="opset1">
			<data auto_broadcast="numpy" m_pythondiv="true" />
			<input>
				<port id="0" precision="I64" />
				<port id="1" precision="I64" />
			</input>
			<output>
				<port id="2" precision="I64" names="/up2/Cast_2_output_0,/up2/Cast_3_output_0,/up2/Div_1_output_0" />
			</output>
		</layer>
		<layer id="154" name="Constant_487" type="Const" version="opset1">
			<data element_type="i64" shape="1" offset="4505104" size="8" />
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="155" name="/up2/Unsqueeze_6" type="Unsqueeze" version="opset1">
			<input>
				<port id="0" precision="I64" />
				<port id="1" precision="I64">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="I64" names="/up2/Unsqueeze_6_output_0">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="156" name="/up2/Sub_3" type="Subtract" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="I64" />
				<port id="1" precision="I64" />
			</input>
			<output>
				<port id="2" precision="I64" names="/up2/Sub_3_output_0" />
			</output>
		</layer>
		<layer id="157" name="Constant_489" type="Const" version="opset1">
			<data element_type="i64" shape="1" offset="4505104" size="8" />
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="158" name="/up2/Unsqueeze_7" type="Unsqueeze" version="opset1">
			<input>
				<port id="0" precision="I64" />
				<port id="1" precision="I64">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="I64" names="/up2/Unsqueeze_7_output_0">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="159" name="/up2/Concat_1" type="Concat" version="opset1">
			<data axis="0" />
			<input>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
				<port id="1" precision="I64">
					<dim>1</dim>
				</port>
				<port id="2" precision="I64">
					<dim>1</dim>
				</port>
				<port id="3" precision="I64">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="4" precision="I64" names="/up2/Cast_4_output_0,/up2/Concat_1_output_0">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="160" name="/up2/ConstantOfShape" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="4538024" size="32" />
			<output>
				<port id="0" precision="I64" names="/up2/ConstantOfShape_output_0">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="161" name="/up2/Concat_2" type="Concat" version="opset1">
			<data axis="0" />
			<input>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="I64" names="/up2/Concat_2_output_0">
					<dim>8</dim>
				</port>
			</output>
		</layer>
		<layer id="162" name="/up2/Constant_8" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="4538056" size="16" />
			<output>
				<port id="0" precision="I64" names="/up2/Constant_8_output_0">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="163" name="/up2/Reshape" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="I64">
					<dim>8</dim>
				</port>
				<port id="1" precision="I64">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="I64" names="/up2/Reshape_output_0">
					<dim>4</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="164" name="/up2/Constant_10" type="Const" version="opset1">
			<data element_type="i64" shape="1" offset="4283376" size="8" />
			<output>
				<port id="0" precision="I64" names="/up2/Constant_10_output_0">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="165" name="/up2/Constant_11" type="Const" version="opset1">
			<data element_type="i64" shape="1" offset="4538072" size="8" />
			<output>
				<port id="0" precision="I64" names="/up2/Constant_11_output_0">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="166" name="/up2/Constant_12" type="Const" version="opset1">
			<data element_type="i64" shape="1" offset="4283376" size="8" />
			<output>
				<port id="0" precision="I64" names="/up2/Constant_12_output_0">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="167" name="/up2/Slice" type="StridedSlice" version="opset1">
			<data begin_mask="0" end_mask="0" new_axis_mask="" shrink_axis_mask="" ellipsis_mask="" />
			<input>
				<port id="0" precision="I64">
					<dim>4</dim>
					<dim>2</dim>
				</port>
				<port id="1" precision="I64">
					<dim>1</dim>
				</port>
				<port id="2" precision="I64">
					<dim>1</dim>
				</port>
				<port id="3" precision="I64">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="4" precision="I64" names="/up2/Slice_output_0">
					<dim>4</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="168" name="Constant_513" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="4538080" size="16" />
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="169" name="/up2/Transpose" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="I64">
					<dim>4</dim>
					<dim>2</dim>
				</port>
				<port id="1" precision="I64">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="I64" names="/up2/Transpose_output_0">
					<dim>2</dim>
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="170" name="Constant_1370" type="Const" version="opset1">
			<data element_type="i64" shape="1" offset="4283376" size="8" />
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="171" name="/up2/Reshape_1" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="I64">
					<dim>2</dim>
					<dim>4</dim>
				</port>
				<port id="1" precision="I64">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="I64" names="/up2/Cast_5_output_0,/up2/Reshape_1_output_0">
					<dim>8</dim>
				</port>
			</output>
		</layer>
		<layer id="172" name="Constant_520" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="4505104" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="173" name="Split_521" type="Split" version="opset1">
			<data num_splits="2" />
			<input>
				<port id="0" precision="I64">
					<dim>8</dim>
				</port>
				<port id="1" precision="I64" />
			</input>
			<output>
				<port id="2" precision="I64">
					<dim>4</dim>
				</port>
				<port id="3" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="174" name="Constant_519" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="4538096" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="175" name="/up2/Pad" type="Pad" version="opset12">
			<data pad_mode="constant" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>16</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
				<port id="2" precision="I64">
					<dim>4</dim>
				</port>
				<port id="3" precision="FP32" />
			</input>
			<output>
				<port id="4" precision="FP32" names="/up2/Pad_output_0">
					<dim>2</dim>
					<dim>16</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="176" name="/up2/Concat_3" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>16</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>2</dim>
					<dim>16</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/up2/Concat_3_output_0">
					<dim>2</dim>
					<dim>32</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="177" name="onnx::Conv_371" type="Const" version="opset1">
			<data element_type="f32" shape="16, 32, 3, 3" offset="4657204" size="18432" />
			<output>
				<port id="0" precision="FP32" names="onnx::Conv_371">
					<dim>16</dim>
					<dim>32</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="178" name="/up2/conv/double_conv/double_conv.0/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>32</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>16</dim>
					<dim>32</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>2</dim>
					<dim>16</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="179" name="Reshape_641" type="Const" version="opset1">
			<data element_type="f32" shape="1, 16, 1, 1" offset="4675636" size="64" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="180" name="/up2/conv/double_conv/double_conv.0/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>16</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/up2/conv/double_conv/double_conv.0/Conv_output_0">
					<dim>2</dim>
					<dim>16</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="181" name="/up2/conv/double_conv/double_conv.2/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>16</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/up2/conv/double_conv/double_conv.2/Relu_output_0">
					<dim>2</dim>
					<dim>16</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="182" name="onnx::Conv_374" type="Const" version="opset1">
			<data element_type="f32" shape="16, 16, 3, 3" offset="4675700" size="9216" />
			<output>
				<port id="0" precision="FP32" names="onnx::Conv_374">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="183" name="/up2/conv/double_conv/double_conv.3/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>16</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>2</dim>
					<dim>16</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="184" name="Reshape_658" type="Const" version="opset1">
			<data element_type="f32" shape="1, 16, 1, 1" offset="4684916" size="64" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="185" name="/up2/conv/double_conv/double_conv.3/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>16</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/up2/conv/double_conv/double_conv.3/Conv_output_0">
					<dim>2</dim>
					<dim>16</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="186" name="/up2/conv/double_conv/double_conv.5/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>16</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/up2/conv/double_conv/double_conv.5/Relu_output_0">
					<dim>2</dim>
					<dim>16</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="187" name="up3.up.weight" type="Const" version="opset1">
			<data element_type="f32" shape="16, 8, 2, 2" offset="4684980" size="2048" />
			<output>
				<port id="0" precision="FP32" names="up3.up.weight">
					<dim>16</dim>
					<dim>8</dim>
					<dim>2</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="188" name="ConvolutionBackpropData_663" type="ConvolutionBackpropData" version="opset1">
			<data strides="2, 2" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" output_padding="0, 0" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>16</dim>
					<dim>32</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>16</dim>
					<dim>8</dim>
					<dim>2</dim>
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>2</dim>
					<dim>8</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="189" name="Reshape_665" type="Const" version="opset1">
			<data element_type="f32" shape="1, 8, 1, 1" offset="4687028" size="32" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="190" name="/up3/up/ConvTranspose" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>8</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/up3/up/ConvTranspose_output_0">
					<dim>2</dim>
					<dim>8</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="191" name="/up3/Shape" type="ShapeOf" version="opset3">
			<data output_type="i64" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>8</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="I64" names="/up3/Shape_2_output_0,/up3/Shape_output_0">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="192" name="/up3/Constant_2" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="4538008" size="8" />
			<output>
				<port id="0" precision="I64" names="/up3/Constant_2_output_0" />
			</output>
		</layer>
		<layer id="193" name="Constant_678" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="4505104" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="194" name="/up3/Gather_2" type="Gather" version="opset8">
			<data batch_dims="0" />
			<input>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
				<port id="1" precision="I64" />
				<port id="2" precision="I64" />
			</input>
			<output>
				<port id="3" precision="I64" names="/up3/Gather_2_output_0" />
			</output>
		</layer>
		<layer id="195" name="/up3/Shape_1" type="ShapeOf" version="opset3">
			<data output_type="i64" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>8</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="I64" names="/up3/Shape_1_output_0,/up3/Shape_3_output_0">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="196" name="/up3/Constant_3" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="4538008" size="8" />
			<output>
				<port id="0" precision="I64" names="/up3/Constant_3_output_0" />
			</output>
		</layer>
		<layer id="197" name="Constant_682" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="4505104" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="198" name="/up3/Gather_3" type="Gather" version="opset8">
			<data batch_dims="0" />
			<input>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
				<port id="1" precision="I64" />
				<port id="2" precision="I64" />
			</input>
			<output>
				<port id="3" precision="I64" names="/up3/Gather_3_output_0" />
			</output>
		</layer>
		<layer id="199" name="/up3/Sub_1" type="Subtract" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="I64" />
				<port id="1" precision="I64" />
			</input>
			<output>
				<port id="2" precision="I64" names="/up3/Sub_1_output_0" />
			</output>
		</layer>
		<layer id="200" name="/up3/Constant_4" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="4538016" size="8" />
			<output>
				<port id="0" precision="I64" names="/up3/Constant_4_output_0" />
			</output>
		</layer>
		<layer id="201" name="/up3/Div" type="Divide" version="opset1">
			<data auto_broadcast="numpy" m_pythondiv="true" />
			<input>
				<port id="0" precision="I64" />
				<port id="1" precision="I64" />
			</input>
			<output>
				<port id="2" precision="I64" names="/up3/Cast_1_output_0,/up3/Cast_output_0,/up3/Div_output_0" />
			</output>
		</layer>
		<layer id="202" name="Constant_704" type="Const" version="opset1">
			<data element_type="i64" shape="1" offset="4505104" size="8" />
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="203" name="/up3/Unsqueeze_4" type="Unsqueeze" version="opset1">
			<input>
				<port id="0" precision="I64" />
				<port id="1" precision="I64">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="I64" names="/up3/Unsqueeze_4_output_0">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="204" name="/up3/Sub_2" type="Subtract" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="I64" />
				<port id="1" precision="I64" />
			</input>
			<output>
				<port id="2" precision="I64" names="/up3/Sub_2_output_0" />
			</output>
		</layer>
		<layer id="205" name="Constant_706" type="Const" version="opset1">
			<data element_type="i64" shape="1" offset="4505104" size="8" />
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="206" name="/up3/Unsqueeze_5" type="Unsqueeze" version="opset1">
			<input>
				<port id="0" precision="I64" />
				<port id="1" precision="I64">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="I64" names="/up3/Unsqueeze_5_output_0">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="207" name="/up3/Constant" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="4538016" size="8" />
			<output>
				<port id="0" precision="I64" names="/up3/Constant_output_0" />
			</output>
		</layer>
		<layer id="208" name="Constant_669" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="4505104" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="209" name="/up3/Gather" type="Gather" version="opset8">
			<data batch_dims="0" />
			<input>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
				<port id="1" precision="I64" />
				<port id="2" precision="I64" />
			</input>
			<output>
				<port id="3" precision="I64" names="/up3/Gather_output_0" />
			</output>
		</layer>
		<layer id="210" name="/up3/Constant_1" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="4538016" size="8" />
			<output>
				<port id="0" precision="I64" names="/up3/Constant_1_output_0" />
			</output>
		</layer>
		<layer id="211" name="Constant_673" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="4505104" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="212" name="/up3/Gather_1" type="Gather" version="opset8">
			<data batch_dims="0" />
			<input>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
				<port id="1" precision="I64" />
				<port id="2" precision="I64" />
			</input>
			<output>
				<port id="3" precision="I64" names="/up3/Gather_1_output_0" />
			</output>
		</layer>
		<layer id="213" name="/up3/Sub" type="Subtract" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="I64" />
				<port id="1" precision="I64" />
			</input>
			<output>
				<port id="2" precision="I64" names="/up3/Sub_output_0" />
			</output>
		</layer>
		<layer id="214" name="/up3/Constant_5" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="4538016" size="8" />
			<output>
				<port id="0" precision="I64" names="/up3/Constant_5_output_0" />
			</output>
		</layer>
		<layer id="215" name="/up3/Div_1" type="Divide" version="opset1">
			<data auto_broadcast="numpy" m_pythondiv="true" />
			<input>
				<port id="0" precision="I64" />
				<port id="1" precision="I64" />
			</input>
			<output>
				<port id="2" precision="I64" names="/up3/Cast_2_output_0,/up3/Cast_3_output_0,/up3/Div_1_output_0" />
			</output>
		</layer>
		<layer id="216" name="Constant_708" type="Const" version="opset1">
			<data element_type="i64" shape="1" offset="4505104" size="8" />
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="217" name="/up3/Unsqueeze_6" type="Unsqueeze" version="opset1">
			<input>
				<port id="0" precision="I64" />
				<port id="1" precision="I64">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="I64" names="/up3/Unsqueeze_6_output_0">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="218" name="/up3/Sub_3" type="Subtract" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="I64" />
				<port id="1" precision="I64" />
			</input>
			<output>
				<port id="2" precision="I64" names="/up3/Sub_3_output_0" />
			</output>
		</layer>
		<layer id="219" name="Constant_710" type="Const" version="opset1">
			<data element_type="i64" shape="1" offset="4505104" size="8" />
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="220" name="/up3/Unsqueeze_7" type="Unsqueeze" version="opset1">
			<input>
				<port id="0" precision="I64" />
				<port id="1" precision="I64">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="I64" names="/up3/Unsqueeze_7_output_0">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="221" name="/up3/Concat_1" type="Concat" version="opset1">
			<data axis="0" />
			<input>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
				<port id="1" precision="I64">
					<dim>1</dim>
				</port>
				<port id="2" precision="I64">
					<dim>1</dim>
				</port>
				<port id="3" precision="I64">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="4" precision="I64" names="/up3/Cast_4_output_0,/up3/Concat_1_output_0">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="222" name="/up3/ConstantOfShape" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="4538024" size="32" />
			<output>
				<port id="0" precision="I64" names="/up3/ConstantOfShape_output_0">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="223" name="/up3/Concat_2" type="Concat" version="opset1">
			<data axis="0" />
			<input>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="I64" names="/up3/Concat_2_output_0">
					<dim>8</dim>
				</port>
			</output>
		</layer>
		<layer id="224" name="/up3/Constant_8" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="4538056" size="16" />
			<output>
				<port id="0" precision="I64" names="/up3/Constant_8_output_0">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="225" name="/up3/Reshape" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="I64">
					<dim>8</dim>
				</port>
				<port id="1" precision="I64">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="I64" names="/up3/Reshape_output_0">
					<dim>4</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="226" name="/up3/Constant_10" type="Const" version="opset1">
			<data element_type="i64" shape="1" offset="4283376" size="8" />
			<output>
				<port id="0" precision="I64" names="/up3/Constant_10_output_0">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="227" name="/up3/Constant_11" type="Const" version="opset1">
			<data element_type="i64" shape="1" offset="4538072" size="8" />
			<output>
				<port id="0" precision="I64" names="/up3/Constant_11_output_0">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="228" name="/up3/Constant_12" type="Const" version="opset1">
			<data element_type="i64" shape="1" offset="4283376" size="8" />
			<output>
				<port id="0" precision="I64" names="/up3/Constant_12_output_0">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="229" name="/up3/Slice" type="StridedSlice" version="opset1">
			<data begin_mask="0" end_mask="0" new_axis_mask="" shrink_axis_mask="" ellipsis_mask="" />
			<input>
				<port id="0" precision="I64">
					<dim>4</dim>
					<dim>2</dim>
				</port>
				<port id="1" precision="I64">
					<dim>1</dim>
				</port>
				<port id="2" precision="I64">
					<dim>1</dim>
				</port>
				<port id="3" precision="I64">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="4" precision="I64" names="/up3/Slice_output_0">
					<dim>4</dim>
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="230" name="Constant_734" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="4538080" size="16" />
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="231" name="/up3/Transpose" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="I64">
					<dim>4</dim>
					<dim>2</dim>
				</port>
				<port id="1" precision="I64">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="I64" names="/up3/Transpose_output_0">
					<dim>2</dim>
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="232" name="Constant_1371" type="Const" version="opset1">
			<data element_type="i64" shape="1" offset="4283376" size="8" />
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="233" name="/up3/Reshape_1" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="I64">
					<dim>2</dim>
					<dim>4</dim>
				</port>
				<port id="1" precision="I64">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="I64" names="/up3/Cast_5_output_0,/up3/Reshape_1_output_0">
					<dim>8</dim>
				</port>
			</output>
		</layer>
		<layer id="234" name="Constant_741" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="4505104" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="235" name="Split_742" type="Split" version="opset1">
			<data num_splits="2" />
			<input>
				<port id="0" precision="I64">
					<dim>8</dim>
				</port>
				<port id="1" precision="I64" />
			</input>
			<output>
				<port id="2" precision="I64">
					<dim>4</dim>
				</port>
				<port id="3" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="236" name="Constant_740" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="4538096" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="237" name="/up3/Pad" type="Pad" version="opset12">
			<data pad_mode="constant" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>8</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
				<port id="2" precision="I64">
					<dim>4</dim>
				</port>
				<port id="3" precision="FP32" />
			</input>
			<output>
				<port id="4" precision="FP32" names="/up3/Pad_output_0">
					<dim>2</dim>
					<dim>8</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="238" name="/up3/Concat_3" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>8</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>2</dim>
					<dim>8</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/up3/Concat_3_output_0">
					<dim>2</dim>
					<dim>16</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="239" name="onnx::Conv_377" type="Const" version="opset1">
			<data element_type="f32" shape="8, 16, 3, 3" offset="4687060" size="4608" />
			<output>
				<port id="0" precision="FP32" names="onnx::Conv_377">
					<dim>8</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="240" name="/up3/conv/double_conv/double_conv.0/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>16</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>8</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>2</dim>
					<dim>8</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="241" name="Reshape_862" type="Const" version="opset1">
			<data element_type="f32" shape="1, 8, 1, 1" offset="4691668" size="32" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="242" name="/up3/conv/double_conv/double_conv.0/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>8</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/up3/conv/double_conv/double_conv.0/Conv_output_0">
					<dim>2</dim>
					<dim>8</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="243" name="/up3/conv/double_conv/double_conv.2/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>8</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/up3/conv/double_conv/double_conv.2/Relu_output_0">
					<dim>2</dim>
					<dim>8</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="244" name="onnx::Conv_380" type="Const" version="opset1">
			<data element_type="f32" shape="8, 8, 3, 3" offset="4691700" size="2304" />
			<output>
				<port id="0" precision="FP32" names="onnx::Conv_380">
					<dim>8</dim>
					<dim>8</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="245" name="/up3/conv/double_conv/double_conv.3/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>8</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>8</dim>
					<dim>8</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>2</dim>
					<dim>8</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="246" name="Reshape_879" type="Const" version="opset1">
			<data element_type="f32" shape="1, 8, 1, 1" offset="4694004" size="32" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="247" name="/up3/conv/double_conv/double_conv.3/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>8</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/up3/conv/double_conv/double_conv.3/Conv_output_0">
					<dim>2</dim>
					<dim>8</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="248" name="/up3/conv/double_conv/double_conv.5/Relu" type="ReLU" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>8</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/up3/conv/double_conv/double_conv.5/Relu_output_0">
					<dim>2</dim>
					<dim>8</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="249" name="outc.conv.weight" type="Const" version="opset1">
			<data element_type="f32" shape="3, 8, 1, 1" offset="4694036" size="96" />
			<output>
				<port id="0" precision="FP32" names="outc.conv.weight">
					<dim>3</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="250" name="/outc/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>8</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>3</dim>
					<dim>8</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>2</dim>
					<dim>3</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="251" name="Reshape_896" type="Const" version="opset1">
			<data element_type="f32" shape="1, 3, 1, 1" offset="4694132" size="12" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="252" name="/outc/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>3</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/outc/conv/Conv_output_0">
					<dim>2</dim>
					<dim>3</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="253" name="output" type="Sigmoid" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>3</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="output">
					<dim>2</dim>
					<dim>3</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="254" name="output/sink_port_0" type="Result" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>3</dim>
					<dim>64</dim>
					<dim>64</dim>
				</port>
			</input>
		</layer>
	</layers>
	<edges>
		<edge from-layer="0" from-port="0" to-layer="2" to-port="0" />
		<edge from-layer="0" from-port="0" to-layer="34" to-port="0" />
		<edge from-layer="1" from-port="0" to-layer="2" to-port="1" />
		<edge from-layer="2" from-port="2" to-layer="4" to-port="0" />
		<edge from-layer="3" from-port="0" to-layer="4" to-port="1" />
		<edge from-layer="4" from-port="2" to-layer="5" to-port="0" />
		<edge from-layer="5" from-port="1" to-layer="7" to-port="0" />
		<edge from-layer="6" from-port="0" to-layer="7" to-port="1" />
		<edge from-layer="7" from-port="2" to-layer="9" to-port="0" />
		<edge from-layer="8" from-port="0" to-layer="9" to-port="1" />
		<edge from-layer="9" from-port="2" to-layer="10" to-port="0" />
		<edge from-layer="10" from-port="1" to-layer="11" to-port="0" />
		<edge from-layer="10" from-port="1" to-layer="191" to-port="0" />
		<edge from-layer="10" from-port="1" to-layer="238" to-port="0" />
		<edge from-layer="11" from-port="1" to-layer="13" to-port="0" />
		<edge from-layer="12" from-port="0" to-layer="13" to-port="1" />
		<edge from-layer="13" from-port="2" to-layer="15" to-port="0" />
		<edge from-layer="14" from-port="0" to-layer="15" to-port="1" />
		<edge from-layer="15" from-port="2" to-layer="16" to-port="0" />
		<edge from-layer="16" from-port="1" to-layer="18" to-port="0" />
		<edge from-layer="17" from-port="0" to-layer="18" to-port="1" />
		<edge from-layer="18" from-port="2" to-layer="20" to-port="0" />
		<edge from-layer="19" from-port="0" to-layer="20" to-port="1" />
		<edge from-layer="20" from-port="2" to-layer="21" to-port="0" />
		<edge from-layer="21" from-port="1" to-layer="22" to-port="0" />
		<edge from-layer="21" from-port="1" to-layer="176" to-port="0" />
		<edge from-layer="21" from-port="1" to-layer="129" to-port="0" />
		<edge from-layer="22" from-port="1" to-layer="24" to-port="0" />
		<edge from-layer="23" from-port="0" to-layer="24" to-port="1" />
		<edge from-layer="24" from-port="2" to-layer="26" to-port="0" />
		<edge from-layer="25" from-port="0" to-layer="26" to-port="1" />
		<edge from-layer="26" from-port="2" to-layer="27" to-port="0" />
		<edge from-layer="27" from-port="1" to-layer="29" to-port="0" />
		<edge from-layer="28" from-port="0" to-layer="29" to-port="1" />
		<edge from-layer="29" from-port="2" to-layer="31" to-port="0" />
		<edge from-layer="30" from-port="0" to-layer="31" to-port="1" />
		<edge from-layer="31" from-port="2" to-layer="32" to-port="0" />
		<edge from-layer="32" from-port="1" to-layer="46" to-port="0" />
		<edge from-layer="32" from-port="1" to-layer="67" to-port="0" />
		<edge from-layer="32" from-port="1" to-layer="114" to-port="0" />
		<edge from-layer="33" from-port="0" to-layer="34" to-port="1" />
		<edge from-layer="34" from-port="2" to-layer="36" to-port="0" />
		<edge from-layer="35" from-port="0" to-layer="36" to-port="1" />
		<edge from-layer="36" from-port="2" to-layer="38" to-port="0" />
		<edge from-layer="37" from-port="0" to-layer="38" to-port="1" />
		<edge from-layer="38" from-port="2" to-layer="39" to-port="0" />
		<edge from-layer="39" from-port="1" to-layer="41" to-port="0" />
		<edge from-layer="40" from-port="0" to-layer="41" to-port="1" />
		<edge from-layer="41" from-port="2" to-layer="43" to-port="0" />
		<edge from-layer="42" from-port="0" to-layer="43" to-port="1" />
		<edge from-layer="43" from-port="2" to-layer="44" to-port="0" />
		<edge from-layer="44" from-port="1" to-layer="62" to-port="0" />
		<edge from-layer="45" from-port="0" to-layer="61" to-port="0" />
		<edge from-layer="46" from-port="1" to-layer="48" to-port="0" />
		<edge from-layer="47" from-port="0" to-layer="48" to-port="1" />
		<edge from-layer="48" from-port="2" to-layer="50" to-port="0" />
		<edge from-layer="49" from-port="0" to-layer="50" to-port="1" />
		<edge from-layer="50" from-port="2" to-layer="51" to-port="0" />
		<edge from-layer="51" from-port="1" to-layer="53" to-port="0" />
		<edge from-layer="52" from-port="0" to-layer="53" to-port="1" />
		<edge from-layer="53" from-port="2" to-layer="55" to-port="0" />
		<edge from-layer="54" from-port="0" to-layer="55" to-port="1" />
		<edge from-layer="55" from-port="2" to-layer="56" to-port="0" />
		<edge from-layer="56" from-port="1" to-layer="57" to-port="0" />
		<edge from-layer="57" from-port="1" to-layer="60" to-port="0" />
		<edge from-layer="58" from-port="0" to-layer="60" to-port="1" />
		<edge from-layer="59" from-port="0" to-layer="60" to-port="2" />
		<edge from-layer="60" from-port="3" to-layer="61" to-port="1" />
		<edge from-layer="61" from-port="2" to-layer="62" to-port="1" />
		<edge from-layer="62" from-port="2" to-layer="64" to-port="0" />
		<edge from-layer="63" from-port="0" to-layer="64" to-port="1" />
		<edge from-layer="64" from-port="2" to-layer="66" to-port="0" />
		<edge from-layer="65" from-port="0" to-layer="66" to-port="1" />
		<edge from-layer="66" from-port="2" to-layer="113" to-port="0" />
		<edge from-layer="66" from-port="2" to-layer="71" to-port="0" />
		<edge from-layer="67" from-port="1" to-layer="85" to-port="0" />
		<edge from-layer="67" from-port="1" to-layer="70" to-port="0" />
		<edge from-layer="68" from-port="0" to-layer="70" to-port="1" />
		<edge from-layer="69" from-port="0" to-layer="70" to-port="2" />
		<edge from-layer="70" from-port="3" to-layer="75" to-port="0" />
		<edge from-layer="71" from-port="1" to-layer="88" to-port="0" />
		<edge from-layer="71" from-port="1" to-layer="74" to-port="0" />
		<edge from-layer="72" from-port="0" to-layer="74" to-port="1" />
		<edge from-layer="73" from-port="0" to-layer="74" to-port="2" />
		<edge from-layer="74" from-port="3" to-layer="75" to-port="1" />
		<edge from-layer="75" from-port="2" to-layer="77" to-port="0" />
		<edge from-layer="75" from-port="2" to-layer="80" to-port="0" />
		<edge from-layer="76" from-port="0" to-layer="77" to-port="1" />
		<edge from-layer="77" from-port="2" to-layer="79" to-port="0" />
		<edge from-layer="77" from-port="2" to-layer="80" to-port="1" />
		<edge from-layer="78" from-port="0" to-layer="79" to-port="1" />
		<edge from-layer="79" from-port="2" to-layer="97" to-port="0" />
		<edge from-layer="80" from-port="2" to-layer="82" to-port="0" />
		<edge from-layer="81" from-port="0" to-layer="82" to-port="1" />
		<edge from-layer="82" from-port="2" to-layer="97" to-port="1" />
		<edge from-layer="83" from-port="0" to-layer="85" to-port="1" />
		<edge from-layer="84" from-port="0" to-layer="85" to-port="2" />
		<edge from-layer="85" from-port="3" to-layer="89" to-port="0" />
		<edge from-layer="86" from-port="0" to-layer="88" to-port="1" />
		<edge from-layer="87" from-port="0" to-layer="88" to-port="2" />
		<edge from-layer="88" from-port="3" to-layer="89" to-port="1" />
		<edge from-layer="89" from-port="2" to-layer="91" to-port="0" />
		<edge from-layer="89" from-port="2" to-layer="94" to-port="0" />
		<edge from-layer="90" from-port="0" to-layer="91" to-port="1" />
		<edge from-layer="91" from-port="2" to-layer="93" to-port="0" />
		<edge from-layer="91" from-port="2" to-layer="94" to-port="1" />
		<edge from-layer="92" from-port="0" to-layer="93" to-port="1" />
		<edge from-layer="93" from-port="2" to-layer="97" to-port="2" />
		<edge from-layer="94" from-port="2" to-layer="96" to-port="0" />
		<edge from-layer="95" from-port="0" to-layer="96" to-port="1" />
		<edge from-layer="96" from-port="2" to-layer="97" to-port="3" />
		<edge from-layer="97" from-port="4" to-layer="99" to-port="0" />
		<edge from-layer="98" from-port="0" to-layer="99" to-port="1" />
		<edge from-layer="99" from-port="2" to-layer="101" to-port="0" />
		<edge from-layer="100" from-port="0" to-layer="101" to-port="1" />
		<edge from-layer="101" from-port="2" to-layer="105" to-port="0" />
		<edge from-layer="102" from-port="0" to-layer="105" to-port="1" />
		<edge from-layer="103" from-port="0" to-layer="105" to-port="2" />
		<edge from-layer="104" from-port="0" to-layer="105" to-port="3" />
		<edge from-layer="105" from-port="4" to-layer="107" to-port="0" />
		<edge from-layer="106" from-port="0" to-layer="107" to-port="1" />
		<edge from-layer="107" from-port="2" to-layer="109" to-port="0" />
		<edge from-layer="108" from-port="0" to-layer="109" to-port="1" />
		<edge from-layer="109" from-port="2" to-layer="111" to-port="0" />
		<edge from-layer="110" from-port="0" to-layer="111" to-port="1" />
		<edge from-layer="111" from-port="2" to-layer="113" to-port="1" />
		<edge from-layer="111" from-port="3" to-layer="113" to-port="2" />
		<edge from-layer="112" from-port="0" to-layer="113" to-port="3" />
		<edge from-layer="113" from-port="4" to-layer="114" to-port="1" />
		<edge from-layer="114" from-port="2" to-layer="116" to-port="0" />
		<edge from-layer="115" from-port="0" to-layer="116" to-port="1" />
		<edge from-layer="116" from-port="2" to-layer="118" to-port="0" />
		<edge from-layer="117" from-port="0" to-layer="118" to-port="1" />
		<edge from-layer="118" from-port="2" to-layer="119" to-port="0" />
		<edge from-layer="119" from-port="1" to-layer="121" to-port="0" />
		<edge from-layer="120" from-port="0" to-layer="121" to-port="1" />
		<edge from-layer="121" from-port="2" to-layer="123" to-port="0" />
		<edge from-layer="122" from-port="0" to-layer="123" to-port="1" />
		<edge from-layer="123" from-port="2" to-layer="124" to-port="0" />
		<edge from-layer="124" from-port="1" to-layer="126" to-port="0" />
		<edge from-layer="125" from-port="0" to-layer="126" to-port="1" />
		<edge from-layer="126" from-port="2" to-layer="128" to-port="0" />
		<edge from-layer="127" from-port="0" to-layer="128" to-port="1" />
		<edge from-layer="128" from-port="2" to-layer="133" to-port="0" />
		<edge from-layer="128" from-port="2" to-layer="175" to-port="0" />
		<edge from-layer="129" from-port="1" to-layer="132" to-port="0" />
		<edge from-layer="129" from-port="1" to-layer="147" to-port="0" />
		<edge from-layer="130" from-port="0" to-layer="132" to-port="1" />
		<edge from-layer="131" from-port="0" to-layer="132" to-port="2" />
		<edge from-layer="132" from-port="3" to-layer="137" to-port="0" />
		<edge from-layer="133" from-port="1" to-layer="150" to-port="0" />
		<edge from-layer="133" from-port="1" to-layer="136" to-port="0" />
		<edge from-layer="134" from-port="0" to-layer="136" to-port="1" />
		<edge from-layer="135" from-port="0" to-layer="136" to-port="2" />
		<edge from-layer="136" from-port="3" to-layer="137" to-port="1" />
		<edge from-layer="137" from-port="2" to-layer="139" to-port="0" />
		<edge from-layer="137" from-port="2" to-layer="142" to-port="0" />
		<edge from-layer="138" from-port="0" to-layer="139" to-port="1" />
		<edge from-layer="139" from-port="2" to-layer="141" to-port="0" />
		<edge from-layer="139" from-port="2" to-layer="142" to-port="1" />
		<edge from-layer="140" from-port="0" to-layer="141" to-port="1" />
		<edge from-layer="141" from-port="2" to-layer="159" to-port="0" />
		<edge from-layer="142" from-port="2" to-layer="144" to-port="0" />
		<edge from-layer="143" from-port="0" to-layer="144" to-port="1" />
		<edge from-layer="144" from-port="2" to-layer="159" to-port="1" />
		<edge from-layer="145" from-port="0" to-layer="147" to-port="1" />
		<edge from-layer="146" from-port="0" to-layer="147" to-port="2" />
		<edge from-layer="147" from-port="3" to-layer="151" to-port="0" />
		<edge from-layer="148" from-port="0" to-layer="150" to-port="1" />
		<edge from-layer="149" from-port="0" to-layer="150" to-port="2" />
		<edge from-layer="150" from-port="3" to-layer="151" to-port="1" />
		<edge from-layer="151" from-port="2" to-layer="153" to-port="0" />
		<edge from-layer="151" from-port="2" to-layer="156" to-port="0" />
		<edge from-layer="152" from-port="0" to-layer="153" to-port="1" />
		<edge from-layer="153" from-port="2" to-layer="156" to-port="1" />
		<edge from-layer="153" from-port="2" to-layer="155" to-port="0" />
		<edge from-layer="154" from-port="0" to-layer="155" to-port="1" />
		<edge from-layer="155" from-port="2" to-layer="159" to-port="2" />
		<edge from-layer="156" from-port="2" to-layer="158" to-port="0" />
		<edge from-layer="157" from-port="0" to-layer="158" to-port="1" />
		<edge from-layer="158" from-port="2" to-layer="159" to-port="3" />
		<edge from-layer="159" from-port="4" to-layer="161" to-port="0" />
		<edge from-layer="160" from-port="0" to-layer="161" to-port="1" />
		<edge from-layer="161" from-port="2" to-layer="163" to-port="0" />
		<edge from-layer="162" from-port="0" to-layer="163" to-port="1" />
		<edge from-layer="163" from-port="2" to-layer="167" to-port="0" />
		<edge from-layer="164" from-port="0" to-layer="167" to-port="1" />
		<edge from-layer="165" from-port="0" to-layer="167" to-port="2" />
		<edge from-layer="166" from-port="0" to-layer="167" to-port="3" />
		<edge from-layer="167" from-port="4" to-layer="169" to-port="0" />
		<edge from-layer="168" from-port="0" to-layer="169" to-port="1" />
		<edge from-layer="169" from-port="2" to-layer="171" to-port="0" />
		<edge from-layer="170" from-port="0" to-layer="171" to-port="1" />
		<edge from-layer="171" from-port="2" to-layer="173" to-port="0" />
		<edge from-layer="172" from-port="0" to-layer="173" to-port="1" />
		<edge from-layer="173" from-port="2" to-layer="175" to-port="1" />
		<edge from-layer="173" from-port="3" to-layer="175" to-port="2" />
		<edge from-layer="174" from-port="0" to-layer="175" to-port="3" />
		<edge from-layer="175" from-port="4" to-layer="176" to-port="1" />
		<edge from-layer="176" from-port="2" to-layer="178" to-port="0" />
		<edge from-layer="177" from-port="0" to-layer="178" to-port="1" />
		<edge from-layer="178" from-port="2" to-layer="180" to-port="0" />
		<edge from-layer="179" from-port="0" to-layer="180" to-port="1" />
		<edge from-layer="180" from-port="2" to-layer="181" to-port="0" />
		<edge from-layer="181" from-port="1" to-layer="183" to-port="0" />
		<edge from-layer="182" from-port="0" to-layer="183" to-port="1" />
		<edge from-layer="183" from-port="2" to-layer="185" to-port="0" />
		<edge from-layer="184" from-port="0" to-layer="185" to-port="1" />
		<edge from-layer="185" from-port="2" to-layer="186" to-port="0" />
		<edge from-layer="186" from-port="1" to-layer="188" to-port="0" />
		<edge from-layer="187" from-port="0" to-layer="188" to-port="1" />
		<edge from-layer="188" from-port="2" to-layer="190" to-port="0" />
		<edge from-layer="189" from-port="0" to-layer="190" to-port="1" />
		<edge from-layer="190" from-port="2" to-layer="237" to-port="0" />
		<edge from-layer="190" from-port="2" to-layer="195" to-port="0" />
		<edge from-layer="191" from-port="1" to-layer="209" to-port="0" />
		<edge from-layer="191" from-port="1" to-layer="194" to-port="0" />
		<edge from-layer="192" from-port="0" to-layer="194" to-port="1" />
		<edge from-layer="193" from-port="0" to-layer="194" to-port="2" />
		<edge from-layer="194" from-port="3" to-layer="199" to-port="0" />
		<edge from-layer="195" from-port="1" to-layer="198" to-port="0" />
		<edge from-layer="195" from-port="1" to-layer="212" to-port="0" />
		<edge from-layer="196" from-port="0" to-layer="198" to-port="1" />
		<edge from-layer="197" from-port="0" to-layer="198" to-port="2" />
		<edge from-layer="198" from-port="3" to-layer="199" to-port="1" />
		<edge from-layer="199" from-port="2" to-layer="204" to-port="0" />
		<edge from-layer="199" from-port="2" to-layer="201" to-port="0" />
		<edge from-layer="200" from-port="0" to-layer="201" to-port="1" />
		<edge from-layer="201" from-port="2" to-layer="203" to-port="0" />
		<edge from-layer="201" from-port="2" to-layer="204" to-port="1" />
		<edge from-layer="202" from-port="0" to-layer="203" to-port="1" />
		<edge from-layer="203" from-port="2" to-layer="221" to-port="0" />
		<edge from-layer="204" from-port="2" to-layer="206" to-port="0" />
		<edge from-layer="205" from-port="0" to-layer="206" to-port="1" />
		<edge from-layer="206" from-port="2" to-layer="221" to-port="1" />
		<edge from-layer="207" from-port="0" to-layer="209" to-port="1" />
		<edge from-layer="208" from-port="0" to-layer="209" to-port="2" />
		<edge from-layer="209" from-port="3" to-layer="213" to-port="0" />
		<edge from-layer="210" from-port="0" to-layer="212" to-port="1" />
		<edge from-layer="211" from-port="0" to-layer="212" to-port="2" />
		<edge from-layer="212" from-port="3" to-layer="213" to-port="1" />
		<edge from-layer="213" from-port="2" to-layer="215" to-port="0" />
		<edge from-layer="213" from-port="2" to-layer="218" to-port="0" />
		<edge from-layer="214" from-port="0" to-layer="215" to-port="1" />
		<edge from-layer="215" from-port="2" to-layer="217" to-port="0" />
		<edge from-layer="215" from-port="2" to-layer="218" to-port="1" />
		<edge from-layer="216" from-port="0" to-layer="217" to-port="1" />
		<edge from-layer="217" from-port="2" to-layer="221" to-port="2" />
		<edge from-layer="218" from-port="2" to-layer="220" to-port="0" />
		<edge from-layer="219" from-port="0" to-layer="220" to-port="1" />
		<edge from-layer="220" from-port="2" to-layer="221" to-port="3" />
		<edge from-layer="221" from-port="4" to-layer="223" to-port="0" />
		<edge from-layer="222" from-port="0" to-layer="223" to-port="1" />
		<edge from-layer="223" from-port="2" to-layer="225" to-port="0" />
		<edge from-layer="224" from-port="0" to-layer="225" to-port="1" />
		<edge from-layer="225" from-port="2" to-layer="229" to-port="0" />
		<edge from-layer="226" from-port="0" to-layer="229" to-port="1" />
		<edge from-layer="227" from-port="0" to-layer="229" to-port="2" />
		<edge from-layer="228" from-port="0" to-layer="229" to-port="3" />
		<edge from-layer="229" from-port="4" to-layer="231" to-port="0" />
		<edge from-layer="230" from-port="0" to-layer="231" to-port="1" />
		<edge from-layer="231" from-port="2" to-layer="233" to-port="0" />
		<edge from-layer="232" from-port="0" to-layer="233" to-port="1" />
		<edge from-layer="233" from-port="2" to-layer="235" to-port="0" />
		<edge from-layer="234" from-port="0" to-layer="235" to-port="1" />
		<edge from-layer="235" from-port="2" to-layer="237" to-port="1" />
		<edge from-layer="235" from-port="3" to-layer="237" to-port="2" />
		<edge from-layer="236" from-port="0" to-layer="237" to-port="3" />
		<edge from-layer="237" from-port="4" to-layer="238" to-port="1" />
		<edge from-layer="238" from-port="2" to-layer="240" to-port="0" />
		<edge from-layer="239" from-port="0" to-layer="240" to-port="1" />
		<edge from-layer="240" from-port="2" to-layer="242" to-port="0" />
		<edge from-layer="241" from-port="0" to-layer="242" to-port="1" />
		<edge from-layer="242" from-port="2" to-layer="243" to-port="0" />
		<edge from-layer="243" from-port="1" to-layer="245" to-port="0" />
		<edge from-layer="244" from-port="0" to-layer="245" to-port="1" />
		<edge from-layer="245" from-port="2" to-layer="247" to-port="0" />
		<edge from-layer="246" from-port="0" to-layer="247" to-port="1" />
		<edge from-layer="247" from-port="2" to-layer="248" to-port="0" />
		<edge from-layer="248" from-port="1" to-layer="250" to-port="0" />
		<edge from-layer="249" from-port="0" to-layer="250" to-port="1" />
		<edge from-layer="250" from-port="2" to-layer="252" to-port="0" />
		<edge from-layer="251" from-port="0" to-layer="252" to-port="1" />
		<edge from-layer="252" from-port="2" to-layer="253" to-port="0" />
		<edge from-layer="253" from-port="1" to-layer="254" to-port="0" />
	</edges>
	<rt_info>
		<Runtime_version value="2023.3.0-13775-ceeafaf64f3-releases/2023/3" />
		<conversion_parameters>
			<compress_to_fp16 value="False" />
			<is_python_object value="False" />
		</conversion_parameters>
	</rt_info>
</net>
