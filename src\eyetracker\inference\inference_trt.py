import numpy as np
import tensorrt as trt
import sys
import os
import pycuda.driver as cuda
import pycuda.autoinit

        
class InferenceTrt:
    def __init__(self, model_file, input_shape, output_shape, conf):
        self.input_shape = input_shape
        self.output_shape = output_shape
        self.batch = self.input_shape[0]
        self.conf = conf
        
        if self.conf['debug']:
            print(f"CUDA Version: {'.'.join([str(x) for x in cuda.get_version()])}")

        self.logger = trt.Logger(trt.Logger.WARNING)
        self.runtime = trt.Runtime(self.logger)
        # engine_path = os.path.join(base_path, f'weights/{conf["weights_version"]}/tensorrt/b{self.batch}/{model_file}.trt')
        self.engine = self.load_engine(model_file)
        self.context = self.engine.create_execution_context()

        # Allocate buffers
        self.inputs, self.outputs, self.bindings, self.stream = self.allocate_buffers(self.engine)  


    def load_engine(self, model_file):
        try:
            base_path = sys._MEIPASS
        except:
            base_path = ''
        
        engine_path = f'{model_file}_b{self.batch}.trt'
        if os.path.isfile(engine_path):
            with open(engine_path, "rb") as f:
                engine = self.runtime.deserialize_cuda_engine(f.read())
            return engine
        onnx_path = os.path.join(base_path, f'weights/{self.conf["weights_version"]}/onnx/b{self.batch}/{model_file}.onnx')
        with trt.Builder(self.logger) as builder, builder.create_network() as network, \
        trt.OnnxParser(network, self.logger) as parser:
            # Load the ONNX model
            with open(onnx_path, 'rb') as model:
                if not parser.parse(model.read()):
                    for error in range(parser.num_errors):
                        print(parser.get_error(error))
                    return None

            # Build the engine
            config = builder.create_builder_config()
            plan = builder.build_serialized_network(network, config)
            engine = self.runtime.deserialize_cuda_engine(plan)
        with open(engine_path, "wb") as fp:
            fp.write(plan)
        return engine

    class HostDeviceMem:
        def __init__(self, host_mem, device_mem):
            self.host = host_mem
            self.device = device_mem

    def allocate_buffers(self, engine):
        inputs, outputs, bindings = [], [], []
        stream = cuda.Stream()

        for i in range(engine.num_io_tensors):
            tensor_name = engine.get_tensor_name(i)
            size = trt.volume(engine.get_tensor_shape(tensor_name))
            dtype = trt.nptype(engine.get_tensor_dtype(tensor_name))

            # Allocate host and device buffers
            host_mem = cuda.pagelocked_empty(size, dtype)
            device_mem = cuda.mem_alloc(host_mem.nbytes)

            # Append the device buffer address to device bindings
            bindings.append(int(device_mem))

            # Append to the appropiate input/output list
            if engine.get_tensor_mode(tensor_name) == trt.TensorIOMode.INPUT:
                inputs.append(self.HostDeviceMem(host_mem, device_mem))
            else:
                outputs.append(self.HostDeviceMem(host_mem, device_mem))

        return inputs, outputs, bindings, stream

    def do_inference(self, input_data):
        # Transfer input data to device
        np.copyto(self.inputs[0].host, input_data.ravel())
        cuda.memcpy_htod_async(self.inputs[0].device, self.inputs[0].host, self.stream)

        # Set tensor address
        for i in range(self.engine.num_io_tensors):
            self.context.set_tensor_address(self.engine.get_tensor_name(i), self.bindings[i])

        # Run inference
        self.context.execute_async_v3(stream_handle=self.stream.handle)

        # Transfer predictions back
        cuda.memcpy_dtoh_async(self.outputs[0].host, self.outputs[0].device, self.stream)

        # Synchronize the stream
        self.stream.synchronize()

        return self.outputs[0].host
    
    
    def inference(self, img):
        # if self.batch == 1:
        #     assert self.input_shape[2:4] == img.shape
        # else:
        #     assert self.input_shape == img.shape
        # img = (img * (1/255.)).astype(np.float32)
        # if self.batch==1 and len(img.shape) != 4:
        #     img = np.expand_dims(img, axis=0)
        #     img = np.stack([img]*self.batch)
        # assert len(img.shape)==4

        res = self.do_inference(img)
        return res.reshape(self.output_shape)
        # print(f'{len(res)=}')
        # print(f'{len(res[0])=}')
        # try:
        #     print(f'{res.shape=}')
        # except:
        #     pass
        # try:
        #     print(f'{res[0].shape=}')
        # except:
        #     pass
        # return res
        # self.inputs[0].host = img
        # trt_outputs = do_inference(self.context, bindings=self.bindings, inputs=self.inputs, outputs=self.outputs, stream=self.stream)
        # trt_outputs = [output.reshape(shape) for output, shape in zip(trt_outputs, [self.output_shape])]
        # return trt_outputs[0][0]
    
    def release(self):
        pass

