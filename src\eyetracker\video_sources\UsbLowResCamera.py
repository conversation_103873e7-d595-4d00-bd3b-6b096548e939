import config
import cv2
from video_sources.UsbCamera import UsbCamera


class UsbLowResCamera(UsbCamera):
    def __init__(self):
        config.USB_CAMERA_CONFIG['Width'] //= 2
        config.USB_CAMERA_CONFIG['Height'] //= 2
        super().__init__()

    def get_frame(self):
        img = super().get_frame()
        if img is not None:
            h,w = img.shape[0:2]
            img = cv2.resize(img, (2*w, 2*h))
        return img

