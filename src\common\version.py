import datetime
import os
import sys
import json

def generate_version(dir: str):
    today = datetime.datetime.now().strftime("%Y%m%d")
    version = today
    old_version = ''

    try:
        with open(dir + '/version.txt') as fp:
            old_version = fp.read()
    except:
        pass

    if old_version.startswith(today):
        for l in [chr(x) for x in range(ord('a'), ord('z')+1)]:
            if today + l > old_version:
                version = today + l
                break

    with open(dir + '/version.txt', 'w') as fp:
        fp.write(version)
    
    return version


def create_version_info(version:str, dir:str):
    # Split version into components (assuming format like 20241214a)
    year = version[:4]
    month = version[4:6]
    day = version[6:8]
    rev = 0
    rev_str = ''
    if len(version) > 8:
        # Convert letter to number (a=1, b=2, etc.)
        rev = ord(version[8]) - ord('a') + 1
        rev_str = version[8]

    # Create version tuple
    version_tuple = (
        int(year),
        int(month),
        int(day),
        rev
    )

    # File version as tuple and string
    filevers = version_tuple
    prodvers = version_tuple
    strfilevers = version
    strprodvers = strfilevers
    
    info = {}
    try:
        with open(dir + '/product_info.json') as fp:
            info = json.load(fp)
    except:
        pass
    
    # PyInstaller version info structure
    VSVersionInfo = {
        'filevers': filevers,
        'prodvers': prodvers,
        'FileVersion': strfilevers,
        'ProductVersion': strprodvers,
        'CompanyName': info.get('CompanyName', 'Neuroiconica'),
        'FileDescription': info.get('FileDescription', 'Eye Tracker Application'),
        'InternalName': info.get('InternalName', 'eyetracker'),
        'LegalCopyright': info.get('LegalCopyright', '© Neuroiconica'),
        'OriginalFilename': info.get('OriginalFilename', 'eyetracker.exe'),
        'ProductName': info.get('ProductName', 'Pathfinder')
    }
    
    return VSVersionInfo

if __name__ == '__main__':
    #args: dir
    dir = '.'
    if len(sys.argv) > 1:
        dir = sys.argv[1]
        if len(dir)==0:
            dir = '.'
            
    version = generate_version(dir)
    
    version_info = create_version_info(version, dir)
    
    # Write to version_info.py in the format PyInstaller expects
    with open(dir + '/version_info.py', 'w', encoding='utf-8') as f:
        f.write('# UTF-8\n')
        f.write('#\n')
        f.write('# For more details about fixed file info \'ffi\' see:\n')
        f.write('# http://msdn.microsoft.com/en-us/library/ms646997.aspx\n')
        f.write('VSVersionInfo(\n')
        f.write(f'  ffi=FixedFileInfo(\n')
        f.write(f'    # filevers and prodvers should be always a tuple with four items: (1, 2, 3, 4)\n')
        f.write(f'    # Set not needed items to zero 0.\n')
        f.write(f'    filevers={version_info["filevers"]},\n')
        f.write(f'    prodvers={version_info["prodvers"]},\n')
        f.write(f'    # Contains a bitmask that specifies the valid bits \'flags\'\n')
        f.write(f'    mask=0x3f,\n')
        f.write(f'    # Contains a bitmask that specifies the Boolean attributes of the file.\n')
        f.write(f'    flags=0x0,\n')
        f.write(f'    # The operating system for which this file was designed.\n')
        f.write(f'    # 0x4 - NT and there is no need to change it.\n')
        f.write(f'    OS=0x40004,\n')
        f.write(f'    # The general type of file.\n')
        f.write(f'    # 0x1 - the file is an application.\n')
        f.write(f'    fileType=0x1,\n')
        f.write(f'    # The function of the file.\n')
        f.write(f'    # 0x0 - the function is not defined for this fileType\n')
        f.write(f'    subtype=0x0,\n')
        f.write(f'    # Creation date and time stamp.\n')
        f.write(f'    date=(0, 0)\n')
        f.write(f'  ),\n')
        f.write(f'  kids=[\n')
        f.write(f'    StringFileInfo(\n')
        f.write(f'      [\n')
        f.write(f'      StringTable(\n')
        f.write(f'        u\'040904B0\',\n')
        f.write(f'        [StringStruct(u\'CompanyName\', u\'{version_info["CompanyName"]}\'),\n')
        f.write(f'        StringStruct(u\'FileDescription\', u\'{version_info["FileDescription"]}\'),\n')
        f.write(f'        StringStruct(u\'FileVersion\', u\'{version_info["FileVersion"]}\'),\n')
        f.write(f'        StringStruct(u\'InternalName\', u\'{version_info["InternalName"]}\'),\n')
        f.write(f'        StringStruct(u\'LegalCopyright\', u\'{version_info["LegalCopyright"]}\'),\n')
        f.write(f'        StringStruct(u\'OriginalFilename\', u\'{version_info["OriginalFilename"]}\'),\n')
        f.write(f'        StringStruct(u\'ProductName\', u\'{version_info["ProductName"]}\'),\n')
        f.write(f'        StringStruct(u\'ProductVersion\', u\'{version_info["ProductVersion"]}\')])\n')
        f.write(f'      ]),\n')
        f.write(f'    VarFileInfo([VarStruct(u\'Translation\', [1033, 1200])])\n')
        f.write(f'  ]\n')
        f.write(f')\n')
    
    print(f"Created version_info.py with version {version}")
