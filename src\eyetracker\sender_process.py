import glob
import time
import os
import requests
import logging

from src.common.config import *


logging.basicConfig(filename='server.log', level=logging.INFO,
                    format="%(asctime)s - [%(levelname)s] -  %(name)s - %(message)s")


class SenderProcess:
    def __init__(self, conf:Conf) -> None:
        self.conf = conf
    
    def send_files(self, files:list[str]):
        headers = {
            'Authorization': f'Bearer {self.conf["token"]}',
            'Content-Type': 'application/json'
        }
        for f in files:
            with open(f) as file:
                data = file.read()
            try:
                response = requests.post(self.conf['cloud_host']+self.conf['cloud_upload_uri'], headers=headers, data=data)
                if response.status_code == 200:
                    #print(f"{f} sent succesfully")
                    os.remove(f)
                else:
                    logging.warning(f"Can not load data to cloud: {data=} {response.status_code} {response.text}")
            except:
                logging.exception(f"Can not load data to cloud")
    def run(self):
        logging.info(f'Sender proc started, PID:{os.getpid()}')
        old_files = glob.glob(f'{self.conf["data_dir"]}/*/diag/*.json')
        files = glob.glob(f'{self.conf["session_data_dir"]}diag/*.json')
        result = [item for item in old_files if item not in files]
        self.send_files(old_files)
        # self.rm_empty_dirs(DATA_DIR)
        if self.conf['send_diagnostics_fpm']==0:
            return
        while True:
            files = glob.glob(f'{self.conf["session_data_dir"]}diag/*.json')
            if len(files) == 0:
                time.sleep(1)
                continue
            self.send_files(files)
            