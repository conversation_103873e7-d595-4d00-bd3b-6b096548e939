import glob
import json
import numpy as np
import cv2

#DATA_DATE = '2024_08_19-15-16-00'
DATA_DATE = '2024_08_20-16-54-32'
#DATA_DATE = '2024_08_20-16-58-23'
#DATA_DIR = f'data/!notebook/{DATA_DATE}/deep_diag/'
DATA_DIR = f'data/!notebook/!calibr/'
WIN_NAME = 'img'
SIZE = 500

files = sorted(glob.glob(DATA_DIR+'raw*.json'))
index = 0
data = []
for f in files:
    with open(f) as fp:
        data.append(json.load(fp))

while True:
    img = np.zeros((SIZE, SIZE, 3), dtype=np.uint8)
    dat = data[index]
    for i, d in enumerate(dat):
        vecs = [d['left']['gaze_vec'], d['right']['gaze_vec']]
        for j, v in enumerate(vecs):
            color = (0, 0, 255) if j else (255, 0, 0)
            x = int(SIZE/2 + SIZE/2*v[0])
            y = int(SIZE + v[1]*SIZE)
            cv2.putText(img, str(i+1), 
                        (x, y), 
                        cv2.FONT_HERSHEY_SIMPLEX, 1, color, 1)
    cv2.putText(img, files[index][-25:], 
                (10, SIZE-10), 
                cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 1)
            
    cv2.imshow(WIN_NAME, img)
    key = cv2.waitKey()
    if key == 27: #Esc
        break
    elif key == ord('q') and index > 0:
        index -= 1
    elif key == ord('w') and index < len(data)-1:
        index += 1
            
    